import tailwindcss from "@tailwindcss/vite";
import { TanStackRouterVite as tanstackRouter } from "@tanstack/router-plugin/vite";
import react from "@vitejs/plugin-react-swc";
import { defineConfig } from "vite";
import tsconfigPaths from "vite-tsconfig-paths";

// https://vite.dev/config/
export default defineConfig({
  plugins: [
    tanstackRouter({ autoCodeSplitting: true, quoteStyle: "double", semicolons: true }),
    react(),
    tsconfigPaths(),
    tailwindcss(),
  ],

  build: {
    rollupOptions: {
      output: {
        // adjust chunk names
        /*
        manualChunks(id) {
          return id.includes("tanstack") ? "tanstack" : id.includes("node_modules") ? "vendor" : null;
        },
        */
      },
    },
  },

  server: {
    proxy: {
      "/api": {
        target: "http://127.0.0.1:5000",
        changeOrigin: true,
      },
    },
  },
});
