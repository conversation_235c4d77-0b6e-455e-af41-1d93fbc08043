# Finanze.Pro

![Finanze.Pro Logo](public/favicon-96x96.png)

Finanze.Pro is a modern personal finance management web application that helps you track your expenses, income, accounts, and budgets. Take control of your financial life with intuitive tools for financial planning and analysis.

## Features

- **Account Management**: Track multiple accounts with different currencies
- **Transaction Tracking**: Record and categorize your expenses and income
- **Budget Planning**: Create and monitor budgets for different spending categories
- **Financial Insights**: Visualize your financial data with charts and statistics
- **Responsive Design**: Works on desktop and mobile devices

## Tech Stack

- **Frontend**: React, TypeScript, Vite
- **UI Components**: Radix UI, shadcn/ui, Tailwind CSS
- **State Management**: TanStack Query, Zustand
- **Form Handling**: React Hook Form, Zod
- **Routing**: TanStack Router
- **API Client**: Axios, OpenAPI TypeScript

## Prerequisites

- Node.js (LTS version, see `.nvmrc`)
- pnpm package manager
- Backend API server running (default: http://127.0.0.1:5000)

## Getting Started

### Installation

1. Clone the repository:

```bash
git clone https://github.com/yourusername/finanze.pro.git
cd finanze.pro/webapp
```

2. Install dependencies:

```bash
pnpm install
```

3. Generate API client (optional, requires backend server running):

```bash
pnpm generate:client
```

### Development

Start the development server:

```bash
pnpm dev
```

This will start the development server at http://localhost:5173.

### Building for Production

Build the application for production:

```bash
pnpm build
```

The built files will be in the `dist` directory.

### Preview Production Build

To preview the production build locally:

```bash
pnpm preview
```

## Project Structure

```
src/
├── api/            # API client configuration and services
├── components/     # Shared UI components
│   ├── blocks/     # Composite components
│   ├── elements/   # Basic UI elements
│   ├── forms/      # Form-related components
│   ├── inputs/     # Input components
│   ├── layout/     # Layout components
│   └── ui/         # UI primitives (based on Radix UI)
├── features/       # Feature modules
│   ├── accounts/   # Account management feature
│   ├── budgets/    # Budget management feature
│   ├── categories/ # Category management feature
│   ├── transactions/ # Transaction management feature
│   └── ...
├── lib/            # Utility functions and helpers
└── routes/         # Application routes
```

## Configuration

### API Configuration

The application is configured to connect to a backend API server. By default, it connects to `http://127.0.0.1:5000`. You can modify this in the `vite.config.ts` file:

```typescript
server: {
  proxy: {
    "/api": {
      target: "http://127.0.0.1:5000",
      changeOrigin: true,
    },
  },
},
```

### OpenAPI Client Generation

The application uses OpenAPI TypeScript to generate API clients. The configuration is in `openapi-ts.config.ts`:

```typescript
import { defineConfig } from "@hey-api/openapi-ts";

export default defineConfig({
  input: "http://127.0.0.1:5000/api/openapi.json",
  output: {
    format: "prettier",
    path: "src/api",
  },
  plugins: [
    "@hey-api/typescript",
    { name: "@hey-api/client-axios", baseUrl: "/api", runtimeConfigPath: "./src/api-config.ts" },
    { name: "@hey-api/sdk" },
    "@tanstack/react-query",
    "zod",
  ],
});
```

## Contributing

Contributions are welcome! Please follow these steps:

1. Fork the repository
2. Create a feature branch: `git checkout -b feature/my-feature`
3. Commit your changes: `git commit -m 'feat: add some feature'`
4. Push to the branch: `git push origin feature/my-feature`
5. Open a pull request

Please follow the [conventional commits](https://www.conventionalcommits.org/) format for your commit messages.

## License

[MIT License](LICENSE)

## Acknowledgements

- [Vite](https://vitejs.dev/)
- [React](https://reactjs.org/)
- [TypeScript](https://www.typescriptlang.org/)
- [Tailwind CSS](https://tailwindcss.com/)
- [shadcn/ui](https://ui.shadcn.com/)
- [Radix UI](https://www.radix-ui.com/)
- [TanStack Query](https://tanstack.com/query)
- [TanStack Router](https://tanstack.com/router)
- [Zustand](https://zustand-demo.pmnd.rs/)
- [React Hook Form](https://react-hook-form.com/)
- [Zod](https://zod.dev/)
