import { format } from "date-fns";

export function formatDate(date: string | Date) {
  return format(date, "MMM d, yyyy");
}

export function formatPercent(value: number | string) {
  return new Intl.NumberFormat(undefined, {
    style: "percent",
    minimumFractionDigits: 0,
    maximumFractionDigits: 2,
    signDisplay: "exceptZero",
  }).format(Number(value) / 100);
}

export function formatAmountShort(value: number | string, currency: string) {
  return new Intl.NumberFormat("en-US", {
    style: "currency",
    currency,
    maximumFractionDigits: 0,
    currencyDisplay: "narrowSymbol",
    notation: "compact",
  }).format(Number(value));
}
