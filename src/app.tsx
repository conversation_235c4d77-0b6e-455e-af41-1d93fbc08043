import { useEffect } from "react";

import { RouterProvider } from "@tanstack/react-router";

import PageLoader from "./components/elements/page-loader";
import { Toaster } from "./components/ui/sonner";
import { useAuth } from "./features/auth/hooks";
import { ConfirmationDialog } from "./features/confirmations";
import router from "./router";

export default function App() {
  const { isAuthenticated, isInitialized, initialize } = useAuth();

  useEffect(() => {
    const controller = new AbortController();

    void initialize(controller.signal);

    return () => {
      controller.abort();
    };
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  if (!isInitialized) {
    return <PageLoader />;
  }

  return (
    <>
      <RouterProvider router={router} context={{ isAuthenticated }} />
      <Toaster />
      <ConfirmationDialog />
    </>
  );
}
