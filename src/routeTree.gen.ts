/* eslint-disable */

// @ts-nocheck

// noinspection JSUnusedGlobalSymbols

// This file was automatically generated by TanStack Router.
// You should NOT make any changes in this file as it will be overwritten.
// Additionally, you should also exclude this file from your linter and/or formatter to prevent it from being checked or modified.

// Import Routes

import { Route as rootRoute } from "./routes/__root";
import { Route as GuestImport } from "./routes/_guest";
import { Route as AuthImport } from "./routes/_auth";
import { Route as AuthIndexImport } from "./routes/_auth/index";
import { Route as GuestSignupImport } from "./routes/_guest/signup";
import { Route as GuestResetPasswordImport } from "./routes/_guest/reset-password";
import { Route as GuestRequestResetPasswordImport } from "./routes/_guest/request-reset-password";
import { Route as GuestLoginImport } from "./routes/_guest/login";
import { Route as AuthTransactionsImport } from "./routes/_auth/transactions";
import { Route as AuthCategoriesImport } from "./routes/_auth/categories";
import { Route as AuthSavingsIndexImport } from "./routes/_auth/savings/index";
import { Route as AuthBudgetsIndexImport } from "./routes/_auth/budgets/index";
import { Route as AuthAccountsIndexImport } from "./routes/_auth/accounts/index";
import { Route as AuthBudgetsBudgetIdImport } from "./routes/_auth/budgets/$budgetId";
import { Route as AuthAccountsAccountIdImport } from "./routes/_auth/accounts/$accountId";

// Create/Update Routes

const GuestRoute = GuestImport.update({
  id: "/_guest",
  getParentRoute: () => rootRoute,
} as any);

const AuthRoute = AuthImport.update({
  id: "/_auth",
  getParentRoute: () => rootRoute,
} as any);

const AuthIndexRoute = AuthIndexImport.update({
  id: "/",
  path: "/",
  getParentRoute: () => AuthRoute,
} as any);

const GuestSignupRoute = GuestSignupImport.update({
  id: "/signup",
  path: "/signup",
  getParentRoute: () => GuestRoute,
} as any);

const GuestResetPasswordRoute = GuestResetPasswordImport.update({
  id: "/reset-password",
  path: "/reset-password",
  getParentRoute: () => GuestRoute,
} as any);

const GuestRequestResetPasswordRoute = GuestRequestResetPasswordImport.update({
  id: "/request-reset-password",
  path: "/request-reset-password",
  getParentRoute: () => GuestRoute,
} as any);

const GuestLoginRoute = GuestLoginImport.update({
  id: "/login",
  path: "/login",
  getParentRoute: () => GuestRoute,
} as any);

const AuthTransactionsRoute = AuthTransactionsImport.update({
  id: "/transactions",
  path: "/transactions",
  getParentRoute: () => AuthRoute,
} as any);

const AuthCategoriesRoute = AuthCategoriesImport.update({
  id: "/categories",
  path: "/categories",
  getParentRoute: () => AuthRoute,
} as any);

const AuthSavingsIndexRoute = AuthSavingsIndexImport.update({
  id: "/savings/",
  path: "/savings/",
  getParentRoute: () => AuthRoute,
} as any);

const AuthBudgetsIndexRoute = AuthBudgetsIndexImport.update({
  id: "/budgets/",
  path: "/budgets/",
  getParentRoute: () => AuthRoute,
} as any);

const AuthAccountsIndexRoute = AuthAccountsIndexImport.update({
  id: "/accounts/",
  path: "/accounts/",
  getParentRoute: () => AuthRoute,
} as any);

const AuthBudgetsBudgetIdRoute = AuthBudgetsBudgetIdImport.update({
  id: "/budgets/$budgetId",
  path: "/budgets/$budgetId",
  getParentRoute: () => AuthRoute,
} as any);

const AuthAccountsAccountIdRoute = AuthAccountsAccountIdImport.update({
  id: "/accounts/$accountId",
  path: "/accounts/$accountId",
  getParentRoute: () => AuthRoute,
} as any);

// Populate the FileRoutesByPath interface

declare module "@tanstack/react-router" {
  interface FileRoutesByPath {
    "/_auth": {
      id: "/_auth";
      path: "";
      fullPath: "";
      preLoaderRoute: typeof AuthImport;
      parentRoute: typeof rootRoute;
    };
    "/_guest": {
      id: "/_guest";
      path: "";
      fullPath: "";
      preLoaderRoute: typeof GuestImport;
      parentRoute: typeof rootRoute;
    };
    "/_auth/categories": {
      id: "/_auth/categories";
      path: "/categories";
      fullPath: "/categories";
      preLoaderRoute: typeof AuthCategoriesImport;
      parentRoute: typeof AuthImport;
    };
    "/_auth/transactions": {
      id: "/_auth/transactions";
      path: "/transactions";
      fullPath: "/transactions";
      preLoaderRoute: typeof AuthTransactionsImport;
      parentRoute: typeof AuthImport;
    };
    "/_guest/login": {
      id: "/_guest/login";
      path: "/login";
      fullPath: "/login";
      preLoaderRoute: typeof GuestLoginImport;
      parentRoute: typeof GuestImport;
    };
    "/_guest/request-reset-password": {
      id: "/_guest/request-reset-password";
      path: "/request-reset-password";
      fullPath: "/request-reset-password";
      preLoaderRoute: typeof GuestRequestResetPasswordImport;
      parentRoute: typeof GuestImport;
    };
    "/_guest/reset-password": {
      id: "/_guest/reset-password";
      path: "/reset-password";
      fullPath: "/reset-password";
      preLoaderRoute: typeof GuestResetPasswordImport;
      parentRoute: typeof GuestImport;
    };
    "/_guest/signup": {
      id: "/_guest/signup";
      path: "/signup";
      fullPath: "/signup";
      preLoaderRoute: typeof GuestSignupImport;
      parentRoute: typeof GuestImport;
    };
    "/_auth/": {
      id: "/_auth/";
      path: "/";
      fullPath: "/";
      preLoaderRoute: typeof AuthIndexImport;
      parentRoute: typeof AuthImport;
    };
    "/_auth/accounts/$accountId": {
      id: "/_auth/accounts/$accountId";
      path: "/accounts/$accountId";
      fullPath: "/accounts/$accountId";
      preLoaderRoute: typeof AuthAccountsAccountIdImport;
      parentRoute: typeof AuthImport;
    };
    "/_auth/budgets/$budgetId": {
      id: "/_auth/budgets/$budgetId";
      path: "/budgets/$budgetId";
      fullPath: "/budgets/$budgetId";
      preLoaderRoute: typeof AuthBudgetsBudgetIdImport;
      parentRoute: typeof AuthImport;
    };
    "/_auth/accounts/": {
      id: "/_auth/accounts/";
      path: "/accounts";
      fullPath: "/accounts";
      preLoaderRoute: typeof AuthAccountsIndexImport;
      parentRoute: typeof AuthImport;
    };
    "/_auth/budgets/": {
      id: "/_auth/budgets/";
      path: "/budgets";
      fullPath: "/budgets";
      preLoaderRoute: typeof AuthBudgetsIndexImport;
      parentRoute: typeof AuthImport;
    };
    "/_auth/savings/": {
      id: "/_auth/savings/";
      path: "/savings";
      fullPath: "/savings";
      preLoaderRoute: typeof AuthSavingsIndexImport;
      parentRoute: typeof AuthImport;
    };
  }
}

// Create and export the route tree

interface AuthRouteChildren {
  AuthCategoriesRoute: typeof AuthCategoriesRoute;
  AuthTransactionsRoute: typeof AuthTransactionsRoute;
  AuthIndexRoute: typeof AuthIndexRoute;
  AuthAccountsAccountIdRoute: typeof AuthAccountsAccountIdRoute;
  AuthBudgetsBudgetIdRoute: typeof AuthBudgetsBudgetIdRoute;
  AuthAccountsIndexRoute: typeof AuthAccountsIndexRoute;
  AuthBudgetsIndexRoute: typeof AuthBudgetsIndexRoute;
  AuthSavingsIndexRoute: typeof AuthSavingsIndexRoute;
}

const AuthRouteChildren: AuthRouteChildren = {
  AuthCategoriesRoute: AuthCategoriesRoute,
  AuthTransactionsRoute: AuthTransactionsRoute,
  AuthIndexRoute: AuthIndexRoute,
  AuthAccountsAccountIdRoute: AuthAccountsAccountIdRoute,
  AuthBudgetsBudgetIdRoute: AuthBudgetsBudgetIdRoute,
  AuthAccountsIndexRoute: AuthAccountsIndexRoute,
  AuthBudgetsIndexRoute: AuthBudgetsIndexRoute,
  AuthSavingsIndexRoute: AuthSavingsIndexRoute,
};

const AuthRouteWithChildren = AuthRoute._addFileChildren(AuthRouteChildren);

interface GuestRouteChildren {
  GuestLoginRoute: typeof GuestLoginRoute;
  GuestRequestResetPasswordRoute: typeof GuestRequestResetPasswordRoute;
  GuestResetPasswordRoute: typeof GuestResetPasswordRoute;
  GuestSignupRoute: typeof GuestSignupRoute;
}

const GuestRouteChildren: GuestRouteChildren = {
  GuestLoginRoute: GuestLoginRoute,
  GuestRequestResetPasswordRoute: GuestRequestResetPasswordRoute,
  GuestResetPasswordRoute: GuestResetPasswordRoute,
  GuestSignupRoute: GuestSignupRoute,
};

const GuestRouteWithChildren = GuestRoute._addFileChildren(GuestRouteChildren);

export interface FileRoutesByFullPath {
  "": typeof GuestRouteWithChildren;
  "/categories": typeof AuthCategoriesRoute;
  "/transactions": typeof AuthTransactionsRoute;
  "/login": typeof GuestLoginRoute;
  "/request-reset-password": typeof GuestRequestResetPasswordRoute;
  "/reset-password": typeof GuestResetPasswordRoute;
  "/signup": typeof GuestSignupRoute;
  "/": typeof AuthIndexRoute;
  "/accounts/$accountId": typeof AuthAccountsAccountIdRoute;
  "/budgets/$budgetId": typeof AuthBudgetsBudgetIdRoute;
  "/accounts": typeof AuthAccountsIndexRoute;
  "/budgets": typeof AuthBudgetsIndexRoute;
  "/savings": typeof AuthSavingsIndexRoute;
}

export interface FileRoutesByTo {
  "": typeof GuestRouteWithChildren;
  "/categories": typeof AuthCategoriesRoute;
  "/transactions": typeof AuthTransactionsRoute;
  "/login": typeof GuestLoginRoute;
  "/request-reset-password": typeof GuestRequestResetPasswordRoute;
  "/reset-password": typeof GuestResetPasswordRoute;
  "/signup": typeof GuestSignupRoute;
  "/": typeof AuthIndexRoute;
  "/accounts/$accountId": typeof AuthAccountsAccountIdRoute;
  "/budgets/$budgetId": typeof AuthBudgetsBudgetIdRoute;
  "/accounts": typeof AuthAccountsIndexRoute;
  "/budgets": typeof AuthBudgetsIndexRoute;
  "/savings": typeof AuthSavingsIndexRoute;
}

export interface FileRoutesById {
  __root__: typeof rootRoute;
  "/_auth": typeof AuthRouteWithChildren;
  "/_guest": typeof GuestRouteWithChildren;
  "/_auth/categories": typeof AuthCategoriesRoute;
  "/_auth/transactions": typeof AuthTransactionsRoute;
  "/_guest/login": typeof GuestLoginRoute;
  "/_guest/request-reset-password": typeof GuestRequestResetPasswordRoute;
  "/_guest/reset-password": typeof GuestResetPasswordRoute;
  "/_guest/signup": typeof GuestSignupRoute;
  "/_auth/": typeof AuthIndexRoute;
  "/_auth/accounts/$accountId": typeof AuthAccountsAccountIdRoute;
  "/_auth/budgets/$budgetId": typeof AuthBudgetsBudgetIdRoute;
  "/_auth/accounts/": typeof AuthAccountsIndexRoute;
  "/_auth/budgets/": typeof AuthBudgetsIndexRoute;
  "/_auth/savings/": typeof AuthSavingsIndexRoute;
}

export interface FileRouteTypes {
  fileRoutesByFullPath: FileRoutesByFullPath;
  fullPaths:
    | ""
    | "/categories"
    | "/transactions"
    | "/login"
    | "/request-reset-password"
    | "/reset-password"
    | "/signup"
    | "/"
    | "/accounts/$accountId"
    | "/budgets/$budgetId"
    | "/accounts"
    | "/budgets"
    | "/savings";
  fileRoutesByTo: FileRoutesByTo;
  to:
    | ""
    | "/categories"
    | "/transactions"
    | "/login"
    | "/request-reset-password"
    | "/reset-password"
    | "/signup"
    | "/"
    | "/accounts/$accountId"
    | "/budgets/$budgetId"
    | "/accounts"
    | "/budgets"
    | "/savings";
  id:
    | "__root__"
    | "/_auth"
    | "/_guest"
    | "/_auth/categories"
    | "/_auth/transactions"
    | "/_guest/login"
    | "/_guest/request-reset-password"
    | "/_guest/reset-password"
    | "/_guest/signup"
    | "/_auth/"
    | "/_auth/accounts/$accountId"
    | "/_auth/budgets/$budgetId"
    | "/_auth/accounts/"
    | "/_auth/budgets/"
    | "/_auth/savings/";
  fileRoutesById: FileRoutesById;
}

export interface RootRouteChildren {
  AuthRoute: typeof AuthRouteWithChildren;
  GuestRoute: typeof GuestRouteWithChildren;
}

const rootRouteChildren: RootRouteChildren = {
  AuthRoute: AuthRouteWithChildren,
  GuestRoute: GuestRouteWithChildren,
};

export const routeTree = rootRoute
  ._addFileChildren(rootRouteChildren)
  ._addFileTypes<FileRouteTypes>();

/* ROUTE_MANIFEST_START
{
  "routes": {
    "__root__": {
      "filePath": "__root.tsx",
      "children": [
        "/_auth",
        "/_guest"
      ]
    },
    "/_auth": {
      "filePath": "_auth.tsx",
      "children": [
        "/_auth/categories",
        "/_auth/transactions",
        "/_auth/",
        "/_auth/accounts/$accountId",
        "/_auth/budgets/$budgetId",
        "/_auth/accounts/",
        "/_auth/budgets/",
        "/_auth/savings/"
      ]
    },
    "/_guest": {
      "filePath": "_guest.tsx",
      "children": [
        "/_guest/login",
        "/_guest/request-reset-password",
        "/_guest/reset-password",
        "/_guest/signup"
      ]
    },
    "/_auth/categories": {
      "filePath": "_auth/categories.tsx",
      "parent": "/_auth"
    },
    "/_auth/transactions": {
      "filePath": "_auth/transactions.tsx",
      "parent": "/_auth"
    },
    "/_guest/login": {
      "filePath": "_guest/login.tsx",
      "parent": "/_guest"
    },
    "/_guest/request-reset-password": {
      "filePath": "_guest/request-reset-password.tsx",
      "parent": "/_guest"
    },
    "/_guest/reset-password": {
      "filePath": "_guest/reset-password.tsx",
      "parent": "/_guest"
    },
    "/_guest/signup": {
      "filePath": "_guest/signup.tsx",
      "parent": "/_guest"
    },
    "/_auth/": {
      "filePath": "_auth/index.tsx",
      "parent": "/_auth"
    },
    "/_auth/accounts/$accountId": {
      "filePath": "_auth/accounts/$accountId.tsx",
      "parent": "/_auth"
    },
    "/_auth/budgets/$budgetId": {
      "filePath": "_auth/budgets/$budgetId.tsx",
      "parent": "/_auth"
    },
    "/_auth/accounts/": {
      "filePath": "_auth/accounts/index.tsx",
      "parent": "/_auth"
    },
    "/_auth/budgets/": {
      "filePath": "_auth/budgets/index.tsx",
      "parent": "/_auth"
    },
    "/_auth/savings/": {
      "filePath": "_auth/savings/index.tsx",
      "parent": "/_auth"
    }
  }
}
ROUTE_MANIFEST_END */
