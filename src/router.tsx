import { createRouter } from "@tanstack/react-router";

import PageLoader from "./components/elements/page-loader";
import queryClient from "./queryClient";
import { routeTree } from "./routeTree.gen";

const router = createRouter({
  routeTree,
  context: { isAuthenticated: undefined!, queryClient },
  defaultPendingComponent: () => <PageLoader />,
});

// Register the router instance for type safety
declare module "@tanstack/react-router" {
  interface Register {
    router: typeof router;
  }
}

export default router;
