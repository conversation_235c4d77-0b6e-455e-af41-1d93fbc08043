import type { TokenRequest } from "~/api";

import { useState } from "react";
import { useForm } from "react-hook-form";

import { zodResolver } from "@hookform/resolvers/zod";
import { Link, useNavigate } from "@tanstack/react-router";
import { LoaderCircle as SpinnerIcon } from "lucide-react";

import Notification from "~/components/blocks/notification";
import InputText from "~/components/inputs/input-text";
import { Button } from "~/components/ui/button";
import { Form } from "~/components/ui/form";
import { useAuth } from "~/features/auth/hooks";
import { TokenRequestSchema } from "~/features/auth/schemas";

export default function LoginForm() {
  const navigate = useNavigate();

  const { login } = useAuth();

  const [error, setError] = useState<string | null>(null);

  const form = useForm<TokenRequest>({
    resolver: zodResolver(TokenRequestSchema),
    defaultValues: { email: "", password: "" },
  });

  const onSubmit = form.handleSubmit(async (data) => {
    setError(null);

    const message = await login(data);
    if (message !== true) {
      setError(message);
    } else {
      await navigate({ to: "/" });
    }
  });

  const isSubmitting = form.formState.isSubmitting;

  return (
    <>
      <Form {...form}>
        <form onSubmit={onSubmit} className="space-y-4">
          {error && <Notification title="Login failed" message={error} />}

          <InputText
            control={form.control}
            name="email"
            type="email"
            label="Email"
            placeholder="<EMAIL>"
            className="min-w-64 sm:min-w-72"
            autoComplete="username"
            required
          />

          <div className="space-y-1">
            <InputText
              control={form.control}
              name="password"
              type="password"
              label="Password"
              placeholder="secret"
              className="min-w-64 sm:min-w-72"
              autoComplete="current-password"
              required
            />
            <div className="text-right">
              <Link to="/request-reset-password" className="text-primary text-xs font-medium hover:underline">
                Forgot password?
              </Link>
            </div>
          </div>

          <div>
            <Button className="w-full pt-2" disabled={isSubmitting}>
              {isSubmitting && <SpinnerIcon className="mr-2 h-4 w-4 animate-spin" />}
              Login
            </Button>
          </div>
        </form>
      </Form>
    </>
  );
}
