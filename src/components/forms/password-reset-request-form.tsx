import type { PasswordResetRequest } from "~/api";

import { useState } from "react";
import { useForm } from "react-hook-form";

import { zodResolver } from "@hookform/resolvers/zod";
import { LoaderCircle as SpinnerIcon } from "lucide-react";

import Notification from "~/components/blocks/notification";
import InputText from "~/components/inputs/input-text";
import { Button } from "~/components/ui/button";
import { Form } from "~/components/ui/form";
import { useAuth } from "~/features/auth/hooks";
import { PasswordResetRequestSchema } from "~/features/auth/schemas";

export default function PasswordResetRequestForm() {
  const { requestPasswordResetEmail } = useAuth();

  const [error, setError] = useState<string | null>(null);
  const [isSuccess, setIsSuccess] = useState(false);

  const form = useForm<PasswordResetRequest>({
    resolver: zodResolver(PasswordResetRequestSchema),
    defaultValues: { email: "" },
  });

  const onSubmit = form.handleSubmit(async (data) => {
    setError(null);
    setIsSuccess(false);

    const message = await requestPasswordResetEmail(data);
    if (message !== true) {
      setError(message);
    } else {
      setIsSuccess(true);
    }
  });

  const isSubmitting = form.formState.isSubmitting;

  return (
    <>
      <Form {...form}>
        <form onSubmit={onSubmit} className="space-y-4">
          {error && <Notification title="Request failed" message={error} />}
          {isSuccess && (
            <Notification
              title="Email sent"
              message="If an account with this email exists, you will receive a password reset link."
              variant="default"
            />
          )}

          <InputText
            control={form.control}
            name="email"
            type="email"
            label="Email"
            placeholder="<EMAIL>"
            className="min-w-64 sm:min-w-72"
            autoComplete="username"
            required
          />

          <div>
            <Button className="w-full pt-2" disabled={isSubmitting}>
              {isSubmitting && <SpinnerIcon className="mr-2 h-4 w-4 animate-spin" />}
              Send Reset Link
            </Button>
          </div>
        </form>
      </Form>
    </>
  );
}
