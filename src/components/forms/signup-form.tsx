import type { CreateUserRequest } from "~/api";

import { useState } from "react";
import { useForm } from "react-hook-form";

import { zod<PERSON>esolver } from "@hookform/resolvers/zod";
import { useNavigate } from "@tanstack/react-router";
import { LoaderCircle as SpinnerIcon } from "lucide-react";

import Notification from "~/components/blocks/notification";
import InputCurrency from "~/components/inputs/input-currency";
import InputText from "~/components/inputs/input-text";
import { Button } from "~/components/ui/button";
import { Form } from "~/components/ui/form";
import { useAuth } from "~/features/auth/hooks";
import { CreateUserSchema } from "~/features/auth/schemas";
import { defaultCurrency } from "~/features/currencies/currencies";

export default function SignupForm() {
  const navigate = useNavigate();

  const { register } = useAuth();

  const [error, setError] = useState<string | null>(null);

  const form = useForm<CreateUserRequest>({
    resolver: zodResolver(CreateUserSchema),
    defaultValues: { email: "", password: "", base_currency: defaultCurrency, name: "" },
  });

  const onSubmit = form.handleSubmit(async (data) => {
    setError(null);

    const message = await register({ ...data, name: data.name?.trim() || null });
    if (message !== true) {
      setError(message);
    } else {
      await navigate({ to: "/" });
    }
  });

  const isSubmitting = form.formState.isSubmitting;

  return (
    <>
      <Form {...form}>
        <form onSubmit={onSubmit} className="space-y-4">
          {error && <Notification title="Signup failed" message={error} />}

          <InputText
            control={form.control}
            name="email"
            type="email"
            label="Email"
            placeholder="<EMAIL>"
            className="min-w-64 sm:min-w-72"
            autoComplete="username"
            required
          />

          <InputText
            control={form.control}
            name="password"
            type="password"
            label="Password"
            placeholder="secret"
            className="min-w-64 sm:min-w-72"
            autoComplete="new-password"
            required
          />

          <InputText
            control={form.control}
            name="name"
            type="text"
            label="Name"
            placeholder="John Doe"
            className="min-w-64 sm:min-w-72"
            hint={<span className="text-xs text-gray-400">Optional</span>}
          />

          <InputCurrency control={form.control} name="base_currency" label="Base currency" />

          <div>
            <Button className="w-full pt-2" disabled={isSubmitting}>
              {isSubmitting && <SpinnerIcon className="mr-2 h-4 w-4 animate-spin" />}
              Signup
            </Button>
          </div>
        </form>
      </Form>
    </>
  );
}
