import type { PasswordReset } from "~/api";

import { useState } from "react";
import { useForm } from "react-hook-form";

import { zodResolver } from "@hookform/resolvers/zod";
import { useNavigate } from "@tanstack/react-router";
import { LoaderCircle as SpinnerIcon } from "lucide-react";

import Notification from "~/components/blocks/notification";
import InputText from "~/components/inputs/input-text";
import { Button } from "~/components/ui/button";
import { Form } from "~/components/ui/form";
import { useAuth } from "~/features/auth/hooks";
import { PasswordResetSchema } from "~/features/auth/schemas";

interface Props {
  token: string;
}

export default function PasswordResetForm({ token }: Props) {
  const navigate = useNavigate();

  const { resetUserPassword } = useAuth();

  const [error, setError] = useState<string | null>(null);
  const [isSuccess, setIsSuccess] = useState(false);

  const form = useForm<PasswordReset>({
    resolver: zodResolver(PasswordResetSchema),
    defaultValues: { password: "", token },
  });

  const onSubmit = form.handleSubmit(async (data) => {
    setError(null);
    setIsSuccess(false);

    const message = await resetUserPassword(data);
    if (message !== true) {
      setError(message);
    } else {
      setIsSuccess(true);
      // Redirect to login page after 2 seconds
      setTimeout(() => {
        void navigate({ to: "/login" });
      }, 2000);
    }
  });

  const isSubmitting = form.formState.isSubmitting;

  return (
    <>
      <Form {...form}>
        <form onSubmit={onSubmit} className="space-y-4">
          {error && <Notification title="Reset failed" message={error} />}
          {isSuccess && (
            <Notification
              title="Password reset successful"
              message="You can now login with your new password. Redirecting to login page..."
              variant="default"
            />
          )}

          <InputText
            control={form.control}
            name="password"
            type="password"
            label="New Password"
            placeholder="Enter your new password"
            className="min-w-64 sm:min-w-72"
            autoComplete="new-password"
            required
          />

          <div>
            <Button className="w-full pt-2" disabled={isSubmitting || isSuccess}>
              {isSubmitting && <SpinnerIcon className="mr-2 h-4 w-4 animate-spin" />}
              Reset Password
            </Button>
          </div>
        </form>
      </Form>
    </>
  );
}
