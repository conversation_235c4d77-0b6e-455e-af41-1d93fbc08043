import type { Control, ControllerRenderProps, FieldPath, FieldValues } from "react-hook-form";

import { useState } from "react";
import { useToggle } from "react-use";

import { CheckIcon, ChevronDownIcon, PlusIcon, XIcon } from "lucide-react";

import { But<PERSON> } from "~/components/ui/button";
import {
  Command,
  CommandEmpty,
  CommandGroup,
  CommandInput,
  CommandItem,
  CommandList,
  CommandSeparator,
} from "~/components/ui/command";
import { FormField, FormItem, FormLabel, FormMessage } from "~/components/ui/form";
import { Input } from "~/components/ui/input";
import { Popover, PopoverContent, PopoverTrigger } from "~/components/ui/popover";
import { cn } from "~/lib/utils";

type ControlProps<TFieldValues extends FieldValues> = {
  control: Control<TFieldValues>;
  name: FieldPath<TFieldValues>;
};

type ComponentProps = {
  label: string;
  groups: string[];
  className?: string;
};

type Props<TFieldValues extends FieldValues> = ControlProps<TFieldValues> & ComponentProps;
// Omit<React.ComponentPropsWithoutRef<typeof Select>, keyof ComponentProps | "name">

export default function InputAccountGroup<TFieldValues extends FieldValues>(props: Props<TFieldValues>) {
  const { label, control, name, className, groups } = props;

  const [createNew, toggleCreateNew] = useToggle(false);
  const [newGroup, setNewGroup] = useState("");

  const handleCreateNew = (field: ControllerRenderProps<TFieldValues, FieldPath<TFieldValues>>) => {
    groups.push(newGroup);
    field.onChange(newGroup);
    toggleCreateNew();
    setNewGroup("");
  };

  return (
    <FormField
      control={control}
      name={name}
      render={({ field }) => (
        <FormItem className={className}>
          <FormLabel>{label}</FormLabel>

          <Popover>
            <PopoverTrigger asChild>
              <Button variant="outline" role="combobox" className="justify-between">
                <span className={cn("truncate", !field.value && "text-muted-foreground")}>
                  {field.value ? field.value : "No group"}
                </span>
                <ChevronDownIcon size={16} className="text-muted-foreground/80 shrink-0" aria-hidden="true" />
              </Button>
            </PopoverTrigger>

            <PopoverContent className="border-input w-full min-w-[var(--radix-popper-anchor-width)] p-0" align="start">
              <Command>
                <CommandInput placeholder="Search for group" />
                <CommandList>
                  <CommandEmpty>No group found.</CommandEmpty>
                  <CommandGroup>
                    {groups.map((group) => (
                      <CommandItem key={group} value={group} onSelect={field.onChange}>
                        {group}
                        {field.value === group && <CheckIcon size={16} className="ml-auto" />}
                      </CommandItem>
                    ))}
                  </CommandGroup>
                  <CommandSeparator />
                  <CommandGroup>
                    {!createNew && (
                      <Button variant="ghost" className="w-full justify-start font-normal" onClick={toggleCreateNew}>
                        <PlusIcon size={16} className="-ms-2 opacity-60" aria-hidden="true" />
                        New group
                      </Button>
                    )}

                    {createNew && (
                      <div className="flex flex-row items-center gap-2">
                        <Input
                          placeholder="New group"
                          name="newGroup"
                          onChange={(e) => {
                            setNewGroup(e.target.value);
                          }}
                          onKeyUp={(e) => {
                            if (e.key === "Enter") {
                              handleCreateNew(field);
                            }
                          }}
                          value={newGroup}
                        />
                        <Button
                          variant="default"
                          disabled={!newGroup}
                          onClick={() => {
                            handleCreateNew(field);
                          }}
                        >
                          <CheckIcon />
                        </Button>

                        <Button
                          variant="outline"
                          onClick={() => {
                            toggleCreateNew();
                            setNewGroup("");
                          }}
                        >
                          <XIcon />
                        </Button>
                      </div>
                    )}
                  </CommandGroup>
                </CommandList>
              </Command>
            </PopoverContent>
          </Popover>

          <FormMessage />
        </FormItem>
      )}
    />
  );
}
