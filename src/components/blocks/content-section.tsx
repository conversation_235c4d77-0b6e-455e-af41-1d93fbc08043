import { useRouter } from "@tanstack/react-router";
import { ChevronLeftIcon } from "lucide-react";

import { cn } from "~/lib/utils";

import { Button } from "../ui/button";

interface Props {
  title: string;
  children: React.ReactNode;
  action?: React.ReactNode;
  className?: string;
  goBack?: boolean;
}

export default function ContentSection({ children, title, action, className, goBack }: Props) {
  const router = useRouter();

  return (
    <div className={cn("flex flex-col gap-2", className)}>
      <div className="flex items-center justify-between gap-4">
        <div className="flex items-center gap-1">
          {goBack && (
            <Button
              variant="outline"
              size="icon"
              onClick={() => {
                router.history.back();
              }}
            >
              <ChevronLeftIcon className="size-6" />
            </Button>
          )}
          <h3 className="text-xl leading-7 font-normal text-gray-600">{title}</h3>
        </div>
        {action}
      </div>

      {children}
    </div>
  );
}
