import { AlertCircle } from "lucide-react";

import { Alert, AlertDescription, AlertTitle } from "~/components/ui/alert";

interface Props {
  title: string;
  message: React.ReactNode;
  variant?: "destructive" | "default";
}

export default function AlertDestructive({ title, message, variant = "destructive" }: Props) {
  return (
    <Alert variant={variant}>
      <AlertCircle className="h-4 w-4" />
      <AlertTitle>{title}</AlertTitle>
      <AlertDescription>{message}</AlertDescription>
    </Alert>
  );
}
