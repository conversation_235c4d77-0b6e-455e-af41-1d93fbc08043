import { cn } from "~/lib/utils";

interface Props {
  title: string;
  children: React.ReactNode;
  action?: React.ReactNode;
  className?: string;
}

export default function ContentBlock({ children, title, action, className }: Props) {
  return (
    <div className={cn("flex flex-col gap-2", className)}>
      <div className="flex items-center justify-between gap-4">
        <h3 className="text-xl leading-7 font-normal text-gray-600">{title}</h3>
        {action}
      </div>
      <div className="grow rounded-xl bg-white px-6 py-5">{children}</div>
    </div>
  );
}
