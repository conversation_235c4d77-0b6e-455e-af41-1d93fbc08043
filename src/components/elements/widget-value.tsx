import { formatCurrency } from "~/features/currencies/formatters";

interface Props {
  label: string;
  currency: string;
  value: string;
  icon: React.ReactNode;
}

export default function WidgetValue({ label, currency, value, icon }: Props) {
  return (
    <div className="flex flex-row gap-2">
      <p>{icon}</p>
      <div>
        <p className="text-gray-02 text-xs">{label}</p>
        <p className="text-dark text-base font-bold">{formatCurrency(currency, value)}</p>
      </div>
    </div>
  );
}
