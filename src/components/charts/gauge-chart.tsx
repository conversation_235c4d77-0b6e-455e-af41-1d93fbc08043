import { useEffect, useState } from "react";

interface GaugeChartProps {
  value: string | number;
  min?: string | number;
  max?: string | number;
  label?: string;
  color?: string;
  className?: string;
  formatValue?: (value: number | string, isValue: boolean) => string;
}

export default function GaugeChart({
  value: valueToShow,
  min = 0,
  max = 100,
  label,
  className,
  formatValue = (value) => Number(value).toFixed(2),
}: GaugeChartProps) {
  const [value, setValue] = useState(0);

  // Animation effect with spring-like easing
  useEffect(() => {
    setValue(Number(valueToShow));
  }, [valueToShow]);

  // Chart parameters
  const percentage = Math.max(0, Math.min(1, (value - Number(min)) / (Number(max) - Number(min))));

  // Calculate needle rotation (180 degrees arc, -90 to center it)
  const rotation = percentage * 180;

  const xEnd = value == 0 ? 20 : 100 + 80 * Math.cos(((180 - rotation) * Math.PI) / 180);
  const yEnd = value == 0 ? 100 : 100 - 80 * Math.sin(((180 - rotation) * Math.PI) / 180);

  return (
    <div className={className}>
      <div className="">
        <svg viewBox="0 0 200 135" width="100%" height="100%" className="text-primary">
          {/* Background arc (gray) */}
          <path
            d="M 20 100 A 80 80 0 0 1 180 100"
            fill="none"
            stroke="#e5e7eb"
            strokeWidth="20"
            strokeLinecap="round"
          />

          {/* Progress arc (teal) */}
          <path
            d={`M 20 100 A 80 80 0 0 1 ${xEnd.toFixed(2)} ${yEnd.toFixed(2)}`}
            fill="none"
            stroke="currentColor"
            strokeWidth="20"
            strokeLinecap="round"
          />

          {/* Needle */}
          <g transform={`translate(100, 100)`}>
            <path
              d="M -60 0 L 0 4 L 0 -4 Z"
              fill="currentColor"
              transform={`rotate(${rotation.toFixed(0)})`}
              className="transition-transform duration-1000 ease-linear"
            />
            <circle cx="0" cy="0" r="8" fill="currentColor" />
          </g>

          <text
            x="20"
            y="125"
            fill="currentColor"
            dominantBaseline="central"
            textAnchor="middle"
            fontSize={10}
            className="text-xs font-medium text-gray-400"
          >
            {formatValue(min, false)}
          </text>

          <text
            x="100"
            y="125"
            fill="currentColor"
            dominantBaseline="central"
            textAnchor="middle"
            className="text-dark text-base font-semibold"
          >
            {formatValue(value, true)}
          </text>

          <text
            x="180"
            y="125"
            fill="currentColor"
            dominantBaseline="central"
            textAnchor="middle"
            className="text-xs font-medium text-gray-400"
          >
            {formatValue(max, false)}
          </text>
        </svg>
      </div>

      <div className="text-dark mt-3 text-center text-xs font-medium">{label}</div>
    </div>
  );
}
