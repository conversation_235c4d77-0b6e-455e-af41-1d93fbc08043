import { useEffect, useState } from "react";

import { VisuallyHidden } from "@radix-ui/react-visually-hidden";
import { useLocation } from "@tanstack/react-router";
import { BellIcon, MenuIcon, PlusIcon } from "lucide-react";

import { Button } from "~/components/ui/button";
import { Sheet, SheetContent, SheetTitle, SheetTrigger } from "~/components/ui/sheet";
import { useCurrentUser } from "~/features/auth/hooks";
import TransactionDialog from "~/features/transactions/components/transaction-dialog";

import Sidebar from "./sidebar";

export default function Header() {
  const currentUser = useCurrentUser();
  const pathname = useLocation({ select: (location) => location.pathname });

  const [isOpen, setOpen] = useState(false);

  useEffect(() => {
    setOpen(false);
  }, [pathname]);

  return (
    <header className="flex items-center gap-4 border-b border-gray-200 px-4 py-5 md:gap-6 md:ps-6 md:pe-8">
      <div className="md:hidden">
        <Sheet open={isOpen} onOpenChange={setOpen}>
          <SheetTrigger asChild>
            <Button variant="ghost" size="icon">
              <MenuIcon className="size-6" />
            </Button>
          </SheetTrigger>
          <SheetContent side="left" className="border-r-dark w-[280px]" aria-describedby={undefined}>
            <VisuallyHidden>
              <SheetTitle>Navigation</SheetTitle>
            </VisuallyHidden>

            <Sidebar />
          </SheetContent>
        </Sheet>
      </div>

      <div className="flex flex-grow flex-col md:flex-row md:items-end md:gap-6">
        <h2 className="text-dark text-xl leading-7 font-bold md:text-2xl">Welcome, {currentUser.name}</h2>
        <p className="text-xs leading-6 text-gray-500 md:text-sm">Today is {new Date().toDateString()}</p>
      </div>

      <div className="flex items-center gap-6">
        <Button variant="ghost" size="icon">
          <BellIcon className="size-6 text-gray-600" fill="currentColor" />
        </Button>

        <TransactionDialog>
          <Button variant="default" size="icon" className="rounded-full" title="Add a new transaction">
            <PlusIcon className="size-6" />
          </Button>
        </TransactionDialog>
      </div>
    </header>
  );
}
