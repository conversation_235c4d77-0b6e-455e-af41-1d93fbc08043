import type { LinkProps } from "@tanstack/react-router";

import { Link } from "@tanstack/react-router";
import {
  ArrowLeftRightIcon,
  BookOpenCheckIcon,
  LayersIcon,
  LayoutDashboardIcon,
  LogOutIcon,
  PiggyBankIcon,
  WalletIcon,
} from "lucide-react";

import { useAuth, useCurrentUser } from "~/features/auth/hooks";
import { cn } from "~/lib/utils";

function NavLink({ to, children }: LinkProps) {
  return (
    <Link
      to={to}
      className="inline-flex items-center gap-3 rounded-md px-4 py-3 text-sm"
      inactiveProps={{ className: "text-secondary hover:bg-white/8 hover:text-white/70" }}
      activeProps={{ className: "bg-primary font-semibold text-white" }}
    >
      {children}
    </Link>
  );
}

interface Props {
  className?: string;
}

export default function Sidebar({ className }: Props) {
  const { logout } = useAuth();

  const currentUser = useCurrentUser();

  return (
    <div className={cn("bg-dark flex h-full flex-col gap-10 px-7 py-12", className)}>
      <div className="text-center">
        <Link to="/" className="text-2xl font-extrabold text-white">
          Finanze.Pro
        </Link>
      </div>

      <div className="grow">
        <nav className="flex flex-col gap-4">
          <NavLink to="/">
            <LayoutDashboardIcon className="size-6" /> Dashboard
          </NavLink>
          <NavLink to="/transactions">
            <ArrowLeftRightIcon className="size-6" /> Transactions
          </NavLink>
          <NavLink to="/budgets">
            <BookOpenCheckIcon className="size-6" /> Budgets
          </NavLink>
          <NavLink to="/accounts">
            <WalletIcon className="size-6" /> Accounts
          </NavLink>
          <NavLink to="/savings">
            <PiggyBankIcon className="size-6" /> Savings
          </NavLink>
          <NavLink to="/categories">
            <LayersIcon className="size-6" /> Categories
          </NavLink>
        </nav>
      </div>

      <div>
        <div className="border-b border-zinc-700 pb-8">
          <button
            onClick={logout}
            className="inline-flex w-full cursor-pointer items-center gap-3 rounded-md bg-white/8 px-4 py-3 text-left text-sm text-white hover:bg-white/10"
          >
            <LogOutIcon className="size-6" /> Logout
          </button>
        </div>

        <div className="flex items-center gap-2 pt-8">
          <img src="/user-placeholder.png" alt="user's photo" className="size-8 rounded-full" />
          <div>
            <p className="text-base leading-tight font-medium text-white">{currentUser.name}</p>
            <p className="text-xs text-white/70">{currentUser.email}</p>
          </div>
        </div>
      </div>
    </div>
  );
}
