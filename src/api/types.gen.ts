// This file is auto-generated by @hey-api/openapi-ts

export type Account = {
  account_type: "cash" | "card" | "bank_account" | "savings" | "loan" | "other";
  base_current_balance: string;
  base_opening_balance: string;
  /**
   * Color in HEX format
   */
  color: string;
  created_at: string;
  currency: "EUR" | "UAH" | "USD";
  current_balance: string;
  description: string | null;
  group: string | null;
  id: string;
  is_active: boolean;
  name: string;
  opening_balance: string;
  overdraft_limit: string;
  updated_at: string;
};

export type AccountCreateRequest = {
  account_type?: "cash" | "card" | "bank_account" | "savings" | "loan" | "other";
  /**
   * Color in HEX format
   */
  color?: string;
  currency: "EUR" | "UAH" | "USD";
  description?: string | null;
  group?: string | null;
  is_active?: boolean;
  name: string;
  opening_balance?: string;
  overdraft_limit?: string;
};

export type AccountSetBalanceRequest = {
  balance: string;
};

export type AccountUpdateRequest = {
  account_type?: "cash" | "card" | "bank_account" | "savings" | "loan" | "other";
  /**
   * Color in HEX format
   */
  color?: string;
  currency?: "EUR" | "UAH" | "USD";
  description?: string | null;
  group?: string | null;
  is_active?: boolean;
  name?: string;
  overdraft_limit?: string;
};

export type Budget = {
  amount: string;
  created_at: string;
  current_period:
    | ({
        [key: string]: unknown;
      } | null)
    | BudgetPeriod;
  description: string | null;
  id: string;
  is_active: boolean;
  /**
   * If True, budget is marked as favorite
   */
  is_favorite: boolean;
  /**
   * If True, amount is a fixed value. If False, amount is a percentage of income.
   */
  is_fixed_amount: boolean;
  name: string;
  period_type: "week" | "month" | "year";
  updated_at: string;
};

export type BudgetPeriod = {
  budget_id: string;
  created_at: string;
  current_amount: string;
  end_date: string;
  id: string;
  is_active: boolean;
  planned_amount: string;
  start_date: string;
  updated_at: string;
};

export type BudgetPeriodsList = {
  items?: Array<BudgetPeriod>;
  pagination?: Pagination;
};

export type BudgetRequest = {
  amount: string;
  description?: string | null;
  is_active?: boolean;
  /**
   * If True, budget is marked as favorite
   */
  is_favorite?: boolean;
  /**
   * If True, amount is a fixed value. If False, amount is a percentage of income.
   */
  is_fixed_amount?: boolean;
  name: string;
  /**
   * Budget period type: week, month, year. Default to `month`
   */
  period_type?: "week" | "month" | "year";
};

export type Category = {
  /**
   * Color in HEX format
   */
  color: string;
  /**
   * Icon name from Lucid icons library
   */
  icon: string | null;
  id: string;
  is_expense: boolean;
  name: string;
};

export type CategoryCreateRequest = {
  /**
   * Color in HEX format
   */
  color?: string;
  /**
   * Icon name from Lucid icons library
   */
  icon?: string | null;
  is_expense?: boolean;
  name: string;
};

export type CategoryUpdateRequest = {
  /**
   * Color in HEX format
   */
  color?: string;
  /**
   * Icon name from Lucid icons library
   */
  icon?: string | null;
  is_expense?: boolean;
  name?: string;
};

export type CompleteTaskRequest = {
  is_completed: boolean;
};

export type CreateUserRequest = {
  base_currency: "EUR" | "UAH" | "USD";
  email: string;
  name?: string | null;
  password: string;
};

export type HttpError = {
  detail?: {
    [key: string]: unknown;
  };
  message?: string;
};

export type MonthlyTotals = {
  /**
   * Total expenses for the month
   */
  expenses: string;
  /**
   * Total income for the month
   */
  income: string;
};

export type Pagination = {
  /**
   * Is there a next page
   */
  has_next: boolean;
  /**
   * Is there a previous page
   */
  has_prev: boolean;
  /**
   * Page number (1-based)
   */
  page: number;
  /**
   * Number of pages
   */
  pages: number;
  /**
   * Items per page
   */
  per_page: number;
  /**
   * Total number of items
   */
  total: number;
};

export type PasswordReset = {
  password: string;
  token: string;
};

export type PasswordResetRequest = {
  email: string;
};

export type PasswordResetResponse = {
  message: string;
};

export type PasswordResetSuccess = {
  message: string;
};

export type SavingsGoal = {
  accounts: Array<Account>;
  created_at: string;
  currency: "EUR" | "UAH" | "USD";
  current_amount: string;
  description: string | null;
  id: string;
  name: string;
  status: "active" | "completed" | "archived";
  target_amount: string | null;
  target_date: string | null;
  updated_at: string;
};

export type SavingsGoalAccountsUpdateRequest = {
  /**
   * List of account IDs to associate with this goal
   */
  account_ids: Array<string>;
};

export type SavingsGoalCreateRequest = {
  /**
   * List of account IDs to associate with this goal
   */
  account_ids: Array<string>;
  currency: "EUR" | "UAH" | "USD";
  description?: string | null;
  name: string;
  target_amount?: string | null;
  target_date?: string | null;
};

export type SavingsGoalUpdateRequest = {
  /**
   * List of account IDs to associate with this goal
   */
  account_ids?: Array<string>;
  description?: string | null;
  name?: string;
  status?: "active" | "completed" | "archived";
  target_amount?: string | null;
  target_date?: string | null;
};

export type Summary = {
  /**
   * Current month totals
   */
  current_month: MonthlyTotals;
  /**
   * Previous month totals
   */
  previous_month: MonthlyTotals;
  /**
   * Total balance at the end of previous month
   */
  previous_month_end_balance: string;
  /**
   * Total balance across all active accounts
   */
  total_balance: string;
};

export type Task = {
  created_at: string;
  current_record:
    | TaskRecord
    | ({
        [key: string]: unknown;
      } | null)
    | null;
  description: string | null;
  /**
   * Whether to send notifications for this task
   */
  enable_notifications: boolean;
  id: string;
  is_active: boolean;
  name: string;
  period_type: "week" | "month" | "quarter" | "year";
  target_amount: string | null;
  updated_at: string;
};

export type TaskRecord = {
  completed_at?: string | null;
  created_at: string;
  end_date: string;
  id: string;
  is_completed: boolean;
  start_date: string;
  task_id: string;
  updated_at: string;
};

export type TaskRecordsList = {
  items?: Array<TaskRecord>;
  pagination?: Pagination;
};

export type TaskRequest = {
  description?: string | null;
  /**
   * Whether to send notifications for this task
   */
  enable_notifications?: boolean;
  is_active?: boolean;
  name: string;
  /**
   * Task period type: week, month, quarter, year. Default to `month`
   */
  period_type?: "week" | "month" | "quarter" | "year";
  target_amount?: string | null;
};

export type TasksList = {
  items?: Array<Task>;
  pagination?: Pagination;
};

export type TokenRequest = {
  email: string;
  password: string;
};

export type TokenResponse = {
  access_token: string;
};

export type Transaction = {
  account: Account;
  account_id: string;
  account_to:
    | ({
        [key: string]: unknown;
      } | null)
    | Account;
  account_to_id: string | null;
  /**
   * Transaction amount in the account currency
   */
  amount: string;
  /**
   * Transfer amount in the destination account currency
   */
  amount_to: string;
  /**
   * Transaction amount in the user's base currency
   */
  base_amount: string;
  /**
   * Transfer amount in the user's base currency
   */
  base_amount_to: string;
  category:
    | ({
        [key: string]: unknown;
      } | null)
    | Category;
  category_id: string | null;
  created_at: string;
  description: string | null;
  id: string;
  /**
   * Optional task ID associated with this transaction
   */
  task_id?: string | null;
  transaction_date: string;
  /**
   * Transaction type: income, expense, or transfer
   */
  transaction_type: "income" | "expense" | "transfer";
  updated_at: string;
};

export type TransactionList = {
  items?: Array<Transaction>;
  pagination?: Pagination;
};

export type TransactionRequest = {
  account_id: string;
  account_to_id?: string | null;
  amount: string;
  amount_to?: string;
  category_id?: string | null;
  description?: string | null;
  /**
   * Optional task ID to associate with this transaction
   */
  task_id?: string | null;
  /**
   * Transaction date
   */
  transaction_date: string;
  /**
   * Transaction type: income, expense, or transfer
   */
  transaction_type?: "income" | "expense" | "transfer";
};

export type User = {
  base_currency: "EUR" | "UAH" | "USD";
  email: string;
  id: string;
  name: string;
};

export type ValidationError = {
  detail?: {
    "<location>"?: {
      "<field_name>"?: Array<string>;
    };
  };
  message?: string;
};

export type GetAccountsData = {
  body?: never;
  path?: never;
  query?: never;
  url: "/api/v1/accounts";
};

export type GetAccountsErrors = {
  /**
   * Authentication error
   */
  401: HttpError;
};

export type GetAccountsError = GetAccountsErrors[keyof GetAccountsErrors];

export type GetAccountsResponses = {
  /**
   * Successful response
   */
  200: Array<Account>;
};

export type GetAccountsResponse = GetAccountsResponses[keyof GetAccountsResponses];

export type CreateAccountData = {
  body?: AccountCreateRequest;
  path?: never;
  query?: never;
  url: "/api/v1/accounts";
};

export type CreateAccountErrors = {
  /**
   * Authentication error
   */
  401: HttpError;
  /**
   * Validation error
   */
  422: ValidationError;
};

export type CreateAccountError = CreateAccountErrors[keyof CreateAccountErrors];

export type CreateAccountResponses = {
  /**
   * Successful response
   */
  201: Account;
};

export type CreateAccountResponse = CreateAccountResponses[keyof CreateAccountResponses];

export type RemoveAccountData = {
  body?: never;
  path: {
    account_id: string;
  };
  query?: never;
  url: "/api/v1/accounts/{account_id}";
};

export type RemoveAccountErrors = {
  /**
   * Authentication error
   */
  401: HttpError;
  /**
   * Not found
   */
  404: HttpError;
};

export type RemoveAccountError = RemoveAccountErrors[keyof RemoveAccountErrors];

export type RemoveAccountResponses = {
  /**
   * Successful response
   */
  204: void;
};

export type RemoveAccountResponse = RemoveAccountResponses[keyof RemoveAccountResponses];

export type GetAccountData = {
  body?: never;
  path: {
    account_id: string;
  };
  query?: never;
  url: "/api/v1/accounts/{account_id}";
};

export type GetAccountErrors = {
  /**
   * Authentication error
   */
  401: HttpError;
  /**
   * Not found
   */
  404: HttpError;
};

export type GetAccountError = GetAccountErrors[keyof GetAccountErrors];

export type GetAccountResponses = {
  /**
   * Successful response
   */
  200: Account;
};

export type GetAccountResponse = GetAccountResponses[keyof GetAccountResponses];

export type UpdateAccountData = {
  body?: AccountUpdateRequest;
  path: {
    account_id: string;
  };
  query?: never;
  url: "/api/v1/accounts/{account_id}";
};

export type UpdateAccountErrors = {
  /**
   * Authentication error
   */
  401: HttpError;
  /**
   * Not found
   */
  404: HttpError;
  /**
   * Validation error
   */
  422: ValidationError;
};

export type UpdateAccountError = UpdateAccountErrors[keyof UpdateAccountErrors];

export type UpdateAccountResponses = {
  /**
   * Successful response
   */
  200: Account;
};

export type UpdateAccountResponse = UpdateAccountResponses[keyof UpdateAccountResponses];

export type SetAccountBalanceData = {
  body?: AccountSetBalanceRequest;
  path: {
    account_id: string;
  };
  query?: never;
  url: "/api/v1/accounts/{account_id}/set-balance";
};

export type SetAccountBalanceErrors = {
  /**
   * Authentication error
   */
  401: HttpError;
  /**
   * Not found
   */
  404: HttpError;
  /**
   * Validation error
   */
  422: ValidationError;
};

export type SetAccountBalanceError = SetAccountBalanceErrors[keyof SetAccountBalanceErrors];

export type SetAccountBalanceResponses = {
  /**
   * Successful response
   */
  200: Account;
};

export type SetAccountBalanceResponse = SetAccountBalanceResponses[keyof SetAccountBalanceResponses];

export type LogoutData = {
  body?: never;
  path?: never;
  query?: never;
  url: "/api/v1/auth/logout";
};

export type LogoutErrors = {
  /**
   * Authentication error
   */
  401: HttpError;
};

export type LogoutError = LogoutErrors[keyof LogoutErrors];

export type LogoutResponses = {
  /**
   * Successful response
   */
  204: void;
};

export type LogoutResponse = LogoutResponses[keyof LogoutResponses];

export type RequestPasswordResetData = {
  body?: PasswordResetRequest;
  path?: never;
  query?: never;
  url: "/api/v1/auth/password-reset/request";
};

export type RequestPasswordResetErrors = {
  /**
   * Validation error
   */
  422: ValidationError;
};

export type RequestPasswordResetError = RequestPasswordResetErrors[keyof RequestPasswordResetErrors];

export type RequestPasswordResetResponses = {
  /**
   * Successful response
   */
  200: PasswordResetResponse;
};

export type RequestPasswordResetResponse = RequestPasswordResetResponses[keyof RequestPasswordResetResponses];

export type ResetPasswordData = {
  body?: PasswordReset;
  path?: never;
  query?: never;
  url: "/api/v1/auth/password-reset/reset";
};

export type ResetPasswordErrors = {
  /**
   * Validation error
   */
  422: ValidationError;
};

export type ResetPasswordError = ResetPasswordErrors[keyof ResetPasswordErrors];

export type ResetPasswordResponses = {
  /**
   * Successful response
   */
  200: PasswordResetSuccess;
};

export type ResetPasswordResponse = ResetPasswordResponses[keyof ResetPasswordResponses];

export type GetTokenData = {
  body?: TokenRequest;
  path?: never;
  query?: never;
  url: "/api/v1/auth/token";
};

export type GetTokenErrors = {
  /**
   * Validation error
   */
  422: ValidationError;
};

export type GetTokenError = GetTokenErrors[keyof GetTokenErrors];

export type GetTokenResponses = {
  /**
   * Successful response
   */
  200: TokenResponse;
};

export type GetTokenResponse = GetTokenResponses[keyof GetTokenResponses];

export type GetBudgetsData = {
  body?: never;
  path?: never;
  query?: never;
  url: "/api/v1/budgets";
};

export type GetBudgetsErrors = {
  /**
   * Authentication error
   */
  401: HttpError;
};

export type GetBudgetsError = GetBudgetsErrors[keyof GetBudgetsErrors];

export type GetBudgetsResponses = {
  /**
   * Successful response
   */
  200: Array<Budget>;
};

export type GetBudgetsResponse = GetBudgetsResponses[keyof GetBudgetsResponses];

export type CreateBudgetData = {
  body?: BudgetRequest;
  path?: never;
  query?: never;
  url: "/api/v1/budgets";
};

export type CreateBudgetErrors = {
  /**
   * Authentication error
   */
  401: HttpError;
  /**
   * Validation error
   */
  422: ValidationError;
};

export type CreateBudgetError = CreateBudgetErrors[keyof CreateBudgetErrors];

export type CreateBudgetResponses = {
  /**
   * Successful response
   */
  201: Budget;
};

export type CreateBudgetResponse = CreateBudgetResponses[keyof CreateBudgetResponses];

export type DeleteBudgetData = {
  body?: never;
  path: {
    budget_id: string;
  };
  query?: never;
  url: "/api/v1/budgets/{budget_id}";
};

export type DeleteBudgetErrors = {
  /**
   * Authentication error
   */
  401: HttpError;
  /**
   * Not found
   */
  404: HttpError;
};

export type DeleteBudgetError = DeleteBudgetErrors[keyof DeleteBudgetErrors];

export type DeleteBudgetResponses = {
  /**
   * Successful response
   */
  204: void;
};

export type DeleteBudgetResponse = DeleteBudgetResponses[keyof DeleteBudgetResponses];

export type GetBudgetData = {
  body?: never;
  path: {
    budget_id: string;
  };
  query?: never;
  url: "/api/v1/budgets/{budget_id}";
};

export type GetBudgetErrors = {
  /**
   * Authentication error
   */
  401: HttpError;
  /**
   * Not found
   */
  404: HttpError;
};

export type GetBudgetError = GetBudgetErrors[keyof GetBudgetErrors];

export type GetBudgetResponses = {
  /**
   * Successful response
   */
  200: Budget;
};

export type GetBudgetResponse = GetBudgetResponses[keyof GetBudgetResponses];

export type UpdateBudgetData = {
  body?: BudgetRequest;
  path: {
    budget_id: string;
  };
  query?: never;
  url: "/api/v1/budgets/{budget_id}";
};

export type UpdateBudgetErrors = {
  /**
   * Authentication error
   */
  401: HttpError;
  /**
   * Not found
   */
  404: HttpError;
  /**
   * Validation error
   */
  422: ValidationError;
};

export type UpdateBudgetError = UpdateBudgetErrors[keyof UpdateBudgetErrors];

export type UpdateBudgetResponses = {
  /**
   * Successful response
   */
  200: Budget;
};

export type UpdateBudgetResponse = UpdateBudgetResponses[keyof UpdateBudgetResponses];

export type GetBudgetPeriodsData = {
  body?: never;
  path: {
    budget_id: string;
  };
  query?: {
    /**
     * Page number (1-based)
     */
    page?: number;
    /**
     * Items per page
     */
    per_page?: number;
  };
  url: "/api/v1/budgets/{budget_id}/periods";
};

export type GetBudgetPeriodsErrors = {
  /**
   * Authentication error
   */
  401: HttpError;
  /**
   * Not found
   */
  404: HttpError;
  /**
   * Validation error
   */
  422: ValidationError;
};

export type GetBudgetPeriodsError = GetBudgetPeriodsErrors[keyof GetBudgetPeriodsErrors];

export type GetBudgetPeriodsResponses = {
  /**
   * Successful response
   */
  200: BudgetPeriodsList;
};

export type GetBudgetPeriodsResponse = GetBudgetPeriodsResponses[keyof GetBudgetPeriodsResponses];

export type UpdateBudgetAmountData = {
  body?: never;
  path: {
    budget_id: string;
  };
  query?: never;
  url: "/api/v1/budgets/{budget_id}/update-amount";
};

export type UpdateBudgetAmountErrors = {
  /**
   * Authentication error
   */
  401: HttpError;
  /**
   * Not found
   */
  404: HttpError;
};

export type UpdateBudgetAmountError = UpdateBudgetAmountErrors[keyof UpdateBudgetAmountErrors];

export type UpdateBudgetAmountResponses = {
  /**
   * Successful response
   */
  200: Budget;
};

export type UpdateBudgetAmountResponse = UpdateBudgetAmountResponses[keyof UpdateBudgetAmountResponses];

export type GetCategoriesData = {
  body?: never;
  path?: never;
  query?: never;
  url: "/api/v1/categories";
};

export type GetCategoriesErrors = {
  /**
   * Authentication error
   */
  401: HttpError;
};

export type GetCategoriesError = GetCategoriesErrors[keyof GetCategoriesErrors];

export type GetCategoriesResponses = {
  /**
   * Successful response
   */
  200: Array<Category>;
};

export type GetCategoriesResponse = GetCategoriesResponses[keyof GetCategoriesResponses];

export type CreateCategoryData = {
  body?: CategoryCreateRequest;
  path?: never;
  query?: never;
  url: "/api/v1/categories";
};

export type CreateCategoryErrors = {
  /**
   * Authentication error
   */
  401: HttpError;
  /**
   * Validation error
   */
  422: ValidationError;
};

export type CreateCategoryError = CreateCategoryErrors[keyof CreateCategoryErrors];

export type CreateCategoryResponses = {
  /**
   * Successful response
   */
  201: Category;
};

export type CreateCategoryResponse = CreateCategoryResponses[keyof CreateCategoryResponses];

export type RemoveCategoryData = {
  body?: never;
  path: {
    category_id: string;
  };
  query?: never;
  url: "/api/v1/categories/{category_id}";
};

export type RemoveCategoryErrors = {
  /**
   * Authentication error
   */
  401: HttpError;
  /**
   * Not found
   */
  404: HttpError;
};

export type RemoveCategoryError = RemoveCategoryErrors[keyof RemoveCategoryErrors];

export type RemoveCategoryResponses = {
  /**
   * Successful response
   */
  204: void;
};

export type RemoveCategoryResponse = RemoveCategoryResponses[keyof RemoveCategoryResponses];

export type GetCategoryData = {
  body?: never;
  path: {
    category_id: string;
  };
  query?: never;
  url: "/api/v1/categories/{category_id}";
};

export type GetCategoryErrors = {
  /**
   * Authentication error
   */
  401: HttpError;
  /**
   * Not found
   */
  404: HttpError;
};

export type GetCategoryError = GetCategoryErrors[keyof GetCategoryErrors];

export type GetCategoryResponses = {
  /**
   * Successful response
   */
  200: Category;
};

export type GetCategoryResponse = GetCategoryResponses[keyof GetCategoryResponses];

export type UpdateCategoryData = {
  body?: CategoryUpdateRequest;
  path: {
    category_id: string;
  };
  query?: never;
  url: "/api/v1/categories/{category_id}";
};

export type UpdateCategoryErrors = {
  /**
   * Authentication error
   */
  401: HttpError;
  /**
   * Not found
   */
  404: HttpError;
  /**
   * Validation error
   */
  422: ValidationError;
};

export type UpdateCategoryError = UpdateCategoryErrors[keyof UpdateCategoryErrors];

export type UpdateCategoryResponses = {
  /**
   * Successful response
   */
  200: Category;
};

export type UpdateCategoryResponse = UpdateCategoryResponses[keyof UpdateCategoryResponses];

export type GetFinancialSummaryData = {
  body?: never;
  path?: never;
  query?: never;
  url: "/api/v1/reports/summary";
};

export type GetFinancialSummaryErrors = {
  /**
   * Authentication error
   */
  401: HttpError;
};

export type GetFinancialSummaryError = GetFinancialSummaryErrors[keyof GetFinancialSummaryErrors];

export type GetFinancialSummaryResponses = {
  /**
   * Successful response
   */
  200: Summary;
};

export type GetFinancialSummaryResponse = GetFinancialSummaryResponses[keyof GetFinancialSummaryResponses];

export type GetSavingsGoalsData = {
  body?: never;
  path?: never;
  query?: never;
  url: "/api/v1/savings/goals";
};

export type GetSavingsGoalsErrors = {
  /**
   * Authentication error
   */
  401: HttpError;
};

export type GetSavingsGoalsError = GetSavingsGoalsErrors[keyof GetSavingsGoalsErrors];

export type GetSavingsGoalsResponses = {
  /**
   * Successful response
   */
  200: Array<SavingsGoal>;
};

export type GetSavingsGoalsResponse = GetSavingsGoalsResponses[keyof GetSavingsGoalsResponses];

export type CreateSavingsGoalData = {
  body?: SavingsGoalCreateRequest;
  path?: never;
  query?: never;
  url: "/api/v1/savings/goals";
};

export type CreateSavingsGoalErrors = {
  /**
   * Authentication error
   */
  401: HttpError;
  /**
   * Validation error
   */
  422: ValidationError;
};

export type CreateSavingsGoalError = CreateSavingsGoalErrors[keyof CreateSavingsGoalErrors];

export type CreateSavingsGoalResponses = {
  /**
   * Successful response
   */
  201: SavingsGoal;
};

export type CreateSavingsGoalResponse = CreateSavingsGoalResponses[keyof CreateSavingsGoalResponses];

export type RemoveSavingsGoalData = {
  body?: never;
  path: {
    goal_id: string;
  };
  query?: never;
  url: "/api/v1/savings/goals/{goal_id}";
};

export type RemoveSavingsGoalErrors = {
  /**
   * Authentication error
   */
  401: HttpError;
  /**
   * Not found
   */
  404: HttpError;
};

export type RemoveSavingsGoalError = RemoveSavingsGoalErrors[keyof RemoveSavingsGoalErrors];

export type RemoveSavingsGoalResponses = {
  /**
   * Successful response
   */
  204: void;
};

export type RemoveSavingsGoalResponse = RemoveSavingsGoalResponses[keyof RemoveSavingsGoalResponses];

export type GetSavingsGoalData = {
  body?: never;
  path: {
    goal_id: string;
  };
  query?: never;
  url: "/api/v1/savings/goals/{goal_id}";
};

export type GetSavingsGoalErrors = {
  /**
   * Authentication error
   */
  401: HttpError;
  /**
   * Not found
   */
  404: HttpError;
};

export type GetSavingsGoalError = GetSavingsGoalErrors[keyof GetSavingsGoalErrors];

export type GetSavingsGoalResponses = {
  /**
   * Successful response
   */
  200: SavingsGoal;
};

export type GetSavingsGoalResponse = GetSavingsGoalResponses[keyof GetSavingsGoalResponses];

export type UpdateSavingsGoalData = {
  body?: SavingsGoalUpdateRequest;
  path: {
    goal_id: string;
  };
  query?: never;
  url: "/api/v1/savings/goals/{goal_id}";
};

export type UpdateSavingsGoalErrors = {
  /**
   * Authentication error
   */
  401: HttpError;
  /**
   * Not found
   */
  404: HttpError;
  /**
   * Validation error
   */
  422: ValidationError;
};

export type UpdateSavingsGoalError = UpdateSavingsGoalErrors[keyof UpdateSavingsGoalErrors];

export type UpdateSavingsGoalResponses = {
  /**
   * Successful response
   */
  200: SavingsGoal;
};

export type UpdateSavingsGoalResponse = UpdateSavingsGoalResponses[keyof UpdateSavingsGoalResponses];

export type UpdateSavingsGoalAccountsData = {
  body?: SavingsGoalAccountsUpdateRequest;
  path: {
    goal_id: string;
  };
  query?: never;
  url: "/api/v1/savings/goals/{goal_id}/accounts";
};

export type UpdateSavingsGoalAccountsErrors = {
  /**
   * Authentication error
   */
  401: HttpError;
  /**
   * Not found
   */
  404: HttpError;
  /**
   * Validation error
   */
  422: ValidationError;
};

export type UpdateSavingsGoalAccountsError = UpdateSavingsGoalAccountsErrors[keyof UpdateSavingsGoalAccountsErrors];

export type UpdateSavingsGoalAccountsResponses = {
  /**
   * Successful response
   */
  200: SavingsGoal;
};

export type UpdateSavingsGoalAccountsResponse =
  UpdateSavingsGoalAccountsResponses[keyof UpdateSavingsGoalAccountsResponses];

export type UpdateSavingsGoalStatusData = {
  body?: never;
  path: {
    goal_id: string;
    status: string;
  };
  query?: never;
  url: "/api/v1/savings/goals/{goal_id}/status/{status}";
};

export type UpdateSavingsGoalStatusErrors = {
  /**
   * Authentication error
   */
  401: HttpError;
  /**
   * Not found
   */
  404: HttpError;
};

export type UpdateSavingsGoalStatusError = UpdateSavingsGoalStatusErrors[keyof UpdateSavingsGoalStatusErrors];

export type UpdateSavingsGoalStatusResponses = {
  /**
   * Successful response
   */
  200: SavingsGoal;
};

export type UpdateSavingsGoalStatusResponse = UpdateSavingsGoalStatusResponses[keyof UpdateSavingsGoalStatusResponses];

export type GetTasksData = {
  body?: never;
  path?: never;
  query?: {
    /**
     * Page number (1-based)
     */
    page?: number;
    /**
     * Items per page
     */
    per_page?: number;
  };
  url: "/api/v1/tasks";
};

export type GetTasksErrors = {
  /**
   * Authentication error
   */
  401: HttpError;
  /**
   * Validation error
   */
  422: ValidationError;
};

export type GetTasksError = GetTasksErrors[keyof GetTasksErrors];

export type GetTasksResponses = {
  /**
   * Successful response
   */
  200: TasksList;
};

export type GetTasksResponse = GetTasksResponses[keyof GetTasksResponses];

export type CreateTaskData = {
  body?: TaskRequest;
  path?: never;
  query?: never;
  url: "/api/v1/tasks";
};

export type CreateTaskErrors = {
  /**
   * Authentication error
   */
  401: HttpError;
  /**
   * Validation error
   */
  422: ValidationError;
};

export type CreateTaskError = CreateTaskErrors[keyof CreateTaskErrors];

export type CreateTaskResponses = {
  /**
   * Successful response
   */
  201: Task;
};

export type CreateTaskResponse = CreateTaskResponses[keyof CreateTaskResponses];

export type GetOverdueTasksData = {
  body?: never;
  path?: never;
  query?: never;
  url: "/api/v1/tasks/overdue";
};

export type GetOverdueTasksErrors = {
  /**
   * Authentication error
   */
  401: HttpError;
};

export type GetOverdueTasksError = GetOverdueTasksErrors[keyof GetOverdueTasksErrors];

export type GetOverdueTasksResponses = {
  /**
   * Successful response
   */
  200: Array<Task>;
};

export type GetOverdueTasksResponse = GetOverdueTasksResponses[keyof GetOverdueTasksResponses];

export type GetUpcomingTasksData = {
  body?: never;
  path?: never;
  query?: never;
  url: "/api/v1/tasks/upcoming";
};

export type GetUpcomingTasksErrors = {
  /**
   * Authentication error
   */
  401: HttpError;
};

export type GetUpcomingTasksError = GetUpcomingTasksErrors[keyof GetUpcomingTasksErrors];

export type GetUpcomingTasksResponses = {
  /**
   * Successful response
   */
  200: Array<Task>;
};

export type GetUpcomingTasksResponse = GetUpcomingTasksResponses[keyof GetUpcomingTasksResponses];

export type DeleteTaskData = {
  body?: never;
  path: {
    task_id: string;
  };
  query?: never;
  url: "/api/v1/tasks/{task_id}";
};

export type DeleteTaskErrors = {
  /**
   * Authentication error
   */
  401: HttpError;
  /**
   * Not found
   */
  404: HttpError;
};

export type DeleteTaskError = DeleteTaskErrors[keyof DeleteTaskErrors];

export type DeleteTaskResponses = {
  /**
   * Successful response
   */
  204: void;
};

export type DeleteTaskResponse = DeleteTaskResponses[keyof DeleteTaskResponses];

export type GetTaskData = {
  body?: never;
  path: {
    task_id: string;
  };
  query?: never;
  url: "/api/v1/tasks/{task_id}";
};

export type GetTaskErrors = {
  /**
   * Authentication error
   */
  401: HttpError;
  /**
   * Not found
   */
  404: HttpError;
};

export type GetTaskError = GetTaskErrors[keyof GetTaskErrors];

export type GetTaskResponses = {
  /**
   * Successful response
   */
  200: Task;
};

export type GetTaskResponse = GetTaskResponses[keyof GetTaskResponses];

export type UpdateTaskData = {
  body?: TaskRequest;
  path: {
    task_id: string;
  };
  query?: never;
  url: "/api/v1/tasks/{task_id}";
};

export type UpdateTaskErrors = {
  /**
   * Authentication error
   */
  401: HttpError;
  /**
   * Not found
   */
  404: HttpError;
  /**
   * Validation error
   */
  422: ValidationError;
};

export type UpdateTaskError = UpdateTaskErrors[keyof UpdateTaskErrors];

export type UpdateTaskResponses = {
  /**
   * Successful response
   */
  200: Task;
};

export type UpdateTaskResponse = UpdateTaskResponses[keyof UpdateTaskResponses];

export type CompleteTaskData = {
  body?: CompleteTaskRequest;
  path: {
    task_id: string;
  };
  query?: never;
  url: "/api/v1/tasks/{task_id}/complete";
};

export type CompleteTaskErrors = {
  /**
   * Authentication error
   */
  401: HttpError;
  /**
   * Not found
   */
  404: HttpError;
  /**
   * Validation error
   */
  422: ValidationError;
};

export type CompleteTaskError = CompleteTaskErrors[keyof CompleteTaskErrors];

export type CompleteTaskResponses = {
  /**
   * Successful response
   */
  200: Task;
};

export type CompleteTaskResponse = CompleteTaskResponses[keyof CompleteTaskResponses];

export type GetTaskRecordsData = {
  body?: never;
  path: {
    task_id: string;
  };
  query?: {
    /**
     * Page number (1-based)
     */
    page?: number;
    /**
     * Items per page
     */
    per_page?: number;
  };
  url: "/api/v1/tasks/{task_id}/records";
};

export type GetTaskRecordsErrors = {
  /**
   * Authentication error
   */
  401: HttpError;
  /**
   * Not found
   */
  404: HttpError;
  /**
   * Validation error
   */
  422: ValidationError;
};

export type GetTaskRecordsError = GetTaskRecordsErrors[keyof GetTaskRecordsErrors];

export type GetTaskRecordsResponses = {
  /**
   * Successful response
   */
  200: TaskRecordsList;
};

export type GetTaskRecordsResponse = GetTaskRecordsResponses[keyof GetTaskRecordsResponses];

export type GetTransactionsData = {
  body?: never;
  path?: never;
  query?: {
    /**
     * Page number (1-based)
     */
    page?: number;
    /**
     * Items per page
     */
    per_page?: number;
    /**
     * Filter by category ID(s). Can be a single UUID or comma-separated list of UUIDs
     */
    category_id?: string | null;
    /**
     * Filter by account ID(s) (includes both source and destination accounts). Can be a single UUID or comma-separated list of UUIDs
     */
    account_id?: string | null;
    /**
     * Filter by transaction type(s). Can be a single type or comma-separated list of types: income, expense, transfer
     */
    transaction_type?: string | null;
  };
  url: "/api/v1/transactions";
};

export type GetTransactionsErrors = {
  /**
   * Authentication error
   */
  401: HttpError;
  /**
   * Validation error
   */
  422: ValidationError;
};

export type GetTransactionsError = GetTransactionsErrors[keyof GetTransactionsErrors];

export type GetTransactionsResponses = {
  /**
   * Successful response
   */
  200: TransactionList;
};

export type GetTransactionsResponse = GetTransactionsResponses[keyof GetTransactionsResponses];

export type CreateTransactionData = {
  body?: TransactionRequest;
  path?: never;
  query?: never;
  url: "/api/v1/transactions";
};

export type CreateTransactionErrors = {
  /**
   * Authentication error
   */
  401: HttpError;
  /**
   * Validation error
   */
  422: ValidationError;
};

export type CreateTransactionError = CreateTransactionErrors[keyof CreateTransactionErrors];

export type CreateTransactionResponses = {
  /**
   * Successful response
   */
  201: Transaction;
};

export type CreateTransactionResponse = CreateTransactionResponses[keyof CreateTransactionResponses];

export type RemoveTransactionData = {
  body?: never;
  path: {
    transaction_id: string;
  };
  query?: never;
  url: "/api/v1/transactions/{transaction_id}";
};

export type RemoveTransactionErrors = {
  /**
   * Authentication error
   */
  401: HttpError;
  /**
   * Not found
   */
  404: HttpError;
};

export type RemoveTransactionError = RemoveTransactionErrors[keyof RemoveTransactionErrors];

export type RemoveTransactionResponses = {
  /**
   * Successful response
   */
  204: void;
};

export type RemoveTransactionResponse = RemoveTransactionResponses[keyof RemoveTransactionResponses];

export type GetTransactionData = {
  body?: never;
  path: {
    transaction_id: string;
  };
  query?: never;
  url: "/api/v1/transactions/{transaction_id}";
};

export type GetTransactionErrors = {
  /**
   * Authentication error
   */
  401: HttpError;
  /**
   * Not found
   */
  404: HttpError;
};

export type GetTransactionError = GetTransactionErrors[keyof GetTransactionErrors];

export type GetTransactionResponses = {
  /**
   * Successful response
   */
  200: Transaction;
};

export type GetTransactionResponse = GetTransactionResponses[keyof GetTransactionResponses];

export type UpdateTransactionData = {
  body?: TransactionRequest;
  path: {
    transaction_id: string;
  };
  query?: never;
  url: "/api/v1/transactions/{transaction_id}";
};

export type UpdateTransactionErrors = {
  /**
   * Authentication error
   */
  401: HttpError;
  /**
   * Not found
   */
  404: HttpError;
  /**
   * Validation error
   */
  422: ValidationError;
};

export type UpdateTransactionError = UpdateTransactionErrors[keyof UpdateTransactionErrors];

export type UpdateTransactionResponses = {
  /**
   * Successful response
   */
  200: Transaction;
};

export type UpdateTransactionResponse = UpdateTransactionResponses[keyof UpdateTransactionResponses];

export type CreateUserData = {
  body?: CreateUserRequest;
  path?: never;
  query?: never;
  url: "/api/v1/users";
};

export type CreateUserErrors = {
  /**
   * Validation error
   */
  422: ValidationError;
};

export type CreateUserError = CreateUserErrors[keyof CreateUserErrors];

export type CreateUserResponses = {
  /**
   * Successful response
   */
  201: User;
};

export type CreateUserResponse = CreateUserResponses[keyof CreateUserResponses];

export type CurrentUserData = {
  body?: never;
  path?: never;
  query?: never;
  url: "/api/v1/users/me";
};

export type CurrentUserErrors = {
  /**
   * Authentication error
   */
  401: HttpError;
};

export type CurrentUserError = CurrentUserErrors[keyof CurrentUserErrors];

export type CurrentUserResponses = {
  /**
   * Successful response
   */
  200: User;
};

export type CurrentUserResponse = CurrentUserResponses[keyof CurrentUserResponses];

export type ClientOptions = {
  baseURL: "http://127.0.0.1:5000" | (string & {});
};
