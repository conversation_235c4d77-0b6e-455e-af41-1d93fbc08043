import type { InfiniteData, UseMutationOptions } from "@tanstack/react-query";
import type { AxiosError } from "axios";
import type { Options } from "../sdk.gen";
import type {
  CompleteTaskData,
  CompleteTaskError,
  CompleteTaskResponse,
  CreateAccountData,
  CreateAccountError,
  CreateAccountResponse,
  CreateBudgetData,
  CreateBudgetError,
  CreateBudgetResponse,
  CreateCategoryData,
  CreateCategoryError,
  CreateCategoryResponse,
  CreateSavingsGoalData,
  CreateSavingsGoalError,
  CreateSavingsGoalResponse,
  CreateTaskData,
  CreateTaskError,
  CreateTaskResponse,
  CreateTransactionData,
  CreateTransactionError,
  CreateTransactionResponse,
  CreateUserData,
  CreateUserError,
  CreateUserResponse,
  CurrentUserData,
  DeleteBudgetData,
  DeleteBudgetError,
  DeleteBudgetResponse,
  DeleteTaskData,
  DeleteTaskError,
  DeleteTaskResponse,
  GetAccountData,
  GetAccountsData,
  GetBudgetData,
  GetBudgetPeriodsData,
  GetBudgetPeriodsError,
  GetBudgetPeriodsResponse,
  GetBudgetsData,
  GetCategoriesData,
  GetCategoryData,
  GetFinancialSummaryData,
  GetOverdueTasksData,
  GetSavingsGoalData,
  GetSavingsGoalsData,
  GetTaskData,
  GetTaskRecordsData,
  GetTaskRecordsError,
  GetTaskRecordsResponse,
  GetTasksData,
  GetTasksError,
  GetTasksResponse,
  GetTokenData,
  GetTokenError,
  GetTokenResponse,
  GetTransactionData,
  GetTransactionsData,
  GetTransactionsError,
  GetTransactionsResponse,
  GetUpcomingTasksData,
  LogoutData,
  LogoutError,
  LogoutResponse,
  RemoveAccountData,
  RemoveAccountError,
  RemoveAccountResponse,
  RemoveCategoryData,
  RemoveCategoryError,
  RemoveCategoryResponse,
  RemoveSavingsGoalData,
  RemoveSavingsGoalError,
  RemoveSavingsGoalResponse,
  RemoveTransactionData,
  RemoveTransactionError,
  RemoveTransactionResponse,
  RequestPasswordResetData,
  RequestPasswordResetError,
  RequestPasswordResetResponse,
  ResetPasswordData,
  ResetPasswordError,
  ResetPasswordResponse,
  SetAccountBalanceData,
  SetAccountBalanceError,
  SetAccountBalanceResponse,
  UpdateAccountData,
  UpdateAccountError,
  UpdateAccountResponse,
  UpdateBudgetAmountData,
  UpdateBudgetAmountError,
  UpdateBudgetAmountResponse,
  UpdateBudgetData,
  UpdateBudgetError,
  UpdateBudgetResponse,
  UpdateCategoryData,
  UpdateCategoryError,
  UpdateCategoryResponse,
  UpdateSavingsGoalAccountsData,
  UpdateSavingsGoalAccountsError,
  UpdateSavingsGoalAccountsResponse,
  UpdateSavingsGoalData,
  UpdateSavingsGoalError,
  UpdateSavingsGoalResponse,
  UpdateSavingsGoalStatusData,
  UpdateSavingsGoalStatusError,
  UpdateSavingsGoalStatusResponse,
  UpdateTaskData,
  UpdateTaskError,
  UpdateTaskResponse,
  UpdateTransactionData,
  UpdateTransactionError,
  UpdateTransactionResponse,
} from "../types.gen";

import { infiniteQueryOptions, queryOptions } from "@tanstack/react-query";

import { client as _heyApiClient } from "../client.gen";
import {
  completeTask,
  createAccount,
  createBudget,
  createCategory,
  createSavingsGoal,
  createTask,
  createTransaction,
  createUser,
  currentUser,
  deleteBudget,
  deleteTask,
  getAccount,
  getAccounts,
  getBudget,
  getBudgetPeriods,
  getBudgets,
  getCategories,
  getCategory,
  getFinancialSummary,
  getOverdueTasks,
  getSavingsGoal,
  getSavingsGoals,
  getTask,
  getTaskRecords,
  getTasks,
  getToken,
  getTransaction,
  getTransactions,
  getUpcomingTasks,
  logout,
  removeAccount,
  removeCategory,
  removeSavingsGoal,
  removeTransaction,
  requestPasswordReset,
  resetPassword,
  setAccountBalance,
  updateAccount,
  updateBudget,
  updateBudgetAmount,
  updateCategory,
  updateSavingsGoal,
  updateSavingsGoalAccounts,
  updateSavingsGoalStatus,
  updateTask,
  updateTransaction,
} from "../sdk.gen";

// This file is auto-generated by @hey-api/openapi-ts

export type QueryKey<TOptions extends Options> = [
  Pick<TOptions, "baseURL" | "body" | "headers" | "path" | "query"> & {
    _id: string;
    _infinite?: boolean;
  },
];

const createQueryKey = <TOptions extends Options>(
  id: string,
  options?: TOptions,
  infinite?: boolean
): [QueryKey<TOptions>[0]] => {
  const params: QueryKey<TOptions>[0] = {
    _id: id,
    baseURL: (options?.client ?? _heyApiClient).getConfig().baseURL,
  } as QueryKey<TOptions>[0];
  if (infinite) {
    params._infinite = infinite;
  }
  if (options?.body) {
    params.body = options.body;
  }
  if (options?.headers) {
    params.headers = options.headers;
  }
  if (options?.path) {
    params.path = options.path;
  }
  if (options?.query) {
    params.query = options.query;
  }
  return [params];
};

export const getAccountsQueryKey = (options?: Options<GetAccountsData>) => createQueryKey("getAccounts", options);

export const getAccountsOptions = (options?: Options<GetAccountsData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getAccounts({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true,
      });
      return data;
    },
    queryKey: getAccountsQueryKey(options),
  });
};

export const createAccountQueryKey = (options?: Options<CreateAccountData>) => createQueryKey("createAccount", options);

export const createAccountOptions = (options?: Options<CreateAccountData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await createAccount({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true,
      });
      return data;
    },
    queryKey: createAccountQueryKey(options),
  });
};

export const createAccountMutation = (options?: Partial<Options<CreateAccountData>>) => {
  const mutationOptions: UseMutationOptions<
    CreateAccountResponse,
    AxiosError<CreateAccountError>,
    Options<CreateAccountData>
  > = {
    mutationFn: async (localOptions) => {
      const { data } = await createAccount({
        ...options,
        ...localOptions,
        throwOnError: true,
      });
      return data;
    },
  };
  return mutationOptions;
};

export const removeAccountMutation = (options?: Partial<Options<RemoveAccountData>>) => {
  const mutationOptions: UseMutationOptions<
    RemoveAccountResponse,
    AxiosError<RemoveAccountError>,
    Options<RemoveAccountData>
  > = {
    mutationFn: async (localOptions) => {
      const { data } = await removeAccount({
        ...options,
        ...localOptions,
        throwOnError: true,
      });
      return data;
    },
  };
  return mutationOptions;
};

export const getAccountQueryKey = (options: Options<GetAccountData>) => createQueryKey("getAccount", options);

export const getAccountOptions = (options: Options<GetAccountData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getAccount({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true,
      });
      return data;
    },
    queryKey: getAccountQueryKey(options),
  });
};

export const updateAccountMutation = (options?: Partial<Options<UpdateAccountData>>) => {
  const mutationOptions: UseMutationOptions<
    UpdateAccountResponse,
    AxiosError<UpdateAccountError>,
    Options<UpdateAccountData>
  > = {
    mutationFn: async (localOptions) => {
      const { data } = await updateAccount({
        ...options,
        ...localOptions,
        throwOnError: true,
      });
      return data;
    },
  };
  return mutationOptions;
};

export const setAccountBalanceQueryKey = (options: Options<SetAccountBalanceData>) =>
  createQueryKey("setAccountBalance", options);

export const setAccountBalanceOptions = (options: Options<SetAccountBalanceData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await setAccountBalance({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true,
      });
      return data;
    },
    queryKey: setAccountBalanceQueryKey(options),
  });
};

export const setAccountBalanceMutation = (options?: Partial<Options<SetAccountBalanceData>>) => {
  const mutationOptions: UseMutationOptions<
    SetAccountBalanceResponse,
    AxiosError<SetAccountBalanceError>,
    Options<SetAccountBalanceData>
  > = {
    mutationFn: async (localOptions) => {
      const { data } = await setAccountBalance({
        ...options,
        ...localOptions,
        throwOnError: true,
      });
      return data;
    },
  };
  return mutationOptions;
};

export const logoutMutation = (options?: Partial<Options<LogoutData>>) => {
  const mutationOptions: UseMutationOptions<LogoutResponse, AxiosError<LogoutError>, Options<LogoutData>> = {
    mutationFn: async (localOptions) => {
      const { data } = await logout({
        ...options,
        ...localOptions,
        throwOnError: true,
      });
      return data;
    },
  };
  return mutationOptions;
};

export const requestPasswordResetQueryKey = (options?: Options<RequestPasswordResetData>) =>
  createQueryKey("requestPasswordReset", options);

export const requestPasswordResetOptions = (options?: Options<RequestPasswordResetData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await requestPasswordReset({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true,
      });
      return data;
    },
    queryKey: requestPasswordResetQueryKey(options),
  });
};

export const requestPasswordResetMutation = (options?: Partial<Options<RequestPasswordResetData>>) => {
  const mutationOptions: UseMutationOptions<
    RequestPasswordResetResponse,
    AxiosError<RequestPasswordResetError>,
    Options<RequestPasswordResetData>
  > = {
    mutationFn: async (localOptions) => {
      const { data } = await requestPasswordReset({
        ...options,
        ...localOptions,
        throwOnError: true,
      });
      return data;
    },
  };
  return mutationOptions;
};

export const resetPasswordQueryKey = (options?: Options<ResetPasswordData>) => createQueryKey("resetPassword", options);

export const resetPasswordOptions = (options?: Options<ResetPasswordData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await resetPassword({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true,
      });
      return data;
    },
    queryKey: resetPasswordQueryKey(options),
  });
};

export const resetPasswordMutation = (options?: Partial<Options<ResetPasswordData>>) => {
  const mutationOptions: UseMutationOptions<
    ResetPasswordResponse,
    AxiosError<ResetPasswordError>,
    Options<ResetPasswordData>
  > = {
    mutationFn: async (localOptions) => {
      const { data } = await resetPassword({
        ...options,
        ...localOptions,
        throwOnError: true,
      });
      return data;
    },
  };
  return mutationOptions;
};

export const getTokenQueryKey = (options?: Options<GetTokenData>) => createQueryKey("getToken", options);

export const getTokenOptions = (options?: Options<GetTokenData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getToken({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true,
      });
      return data;
    },
    queryKey: getTokenQueryKey(options),
  });
};

export const getTokenMutation = (options?: Partial<Options<GetTokenData>>) => {
  const mutationOptions: UseMutationOptions<GetTokenResponse, AxiosError<GetTokenError>, Options<GetTokenData>> = {
    mutationFn: async (localOptions) => {
      const { data } = await getToken({
        ...options,
        ...localOptions,
        throwOnError: true,
      });
      return data;
    },
  };
  return mutationOptions;
};

export const getBudgetsQueryKey = (options?: Options<GetBudgetsData>) => createQueryKey("getBudgets", options);

export const getBudgetsOptions = (options?: Options<GetBudgetsData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getBudgets({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true,
      });
      return data;
    },
    queryKey: getBudgetsQueryKey(options),
  });
};

export const createBudgetQueryKey = (options?: Options<CreateBudgetData>) => createQueryKey("createBudget", options);

export const createBudgetOptions = (options?: Options<CreateBudgetData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await createBudget({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true,
      });
      return data;
    },
    queryKey: createBudgetQueryKey(options),
  });
};

export const createBudgetMutation = (options?: Partial<Options<CreateBudgetData>>) => {
  const mutationOptions: UseMutationOptions<
    CreateBudgetResponse,
    AxiosError<CreateBudgetError>,
    Options<CreateBudgetData>
  > = {
    mutationFn: async (localOptions) => {
      const { data } = await createBudget({
        ...options,
        ...localOptions,
        throwOnError: true,
      });
      return data;
    },
  };
  return mutationOptions;
};

export const deleteBudgetMutation = (options?: Partial<Options<DeleteBudgetData>>) => {
  const mutationOptions: UseMutationOptions<
    DeleteBudgetResponse,
    AxiosError<DeleteBudgetError>,
    Options<DeleteBudgetData>
  > = {
    mutationFn: async (localOptions) => {
      const { data } = await deleteBudget({
        ...options,
        ...localOptions,
        throwOnError: true,
      });
      return data;
    },
  };
  return mutationOptions;
};

export const getBudgetQueryKey = (options: Options<GetBudgetData>) => createQueryKey("getBudget", options);

export const getBudgetOptions = (options: Options<GetBudgetData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getBudget({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true,
      });
      return data;
    },
    queryKey: getBudgetQueryKey(options),
  });
};

export const updateBudgetMutation = (options?: Partial<Options<UpdateBudgetData>>) => {
  const mutationOptions: UseMutationOptions<
    UpdateBudgetResponse,
    AxiosError<UpdateBudgetError>,
    Options<UpdateBudgetData>
  > = {
    mutationFn: async (localOptions) => {
      const { data } = await updateBudget({
        ...options,
        ...localOptions,
        throwOnError: true,
      });
      return data;
    },
  };
  return mutationOptions;
};

export const getBudgetPeriodsQueryKey = (options: Options<GetBudgetPeriodsData>) =>
  createQueryKey("getBudgetPeriods", options);

export const getBudgetPeriodsOptions = (options: Options<GetBudgetPeriodsData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getBudgetPeriods({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true,
      });
      return data;
    },
    queryKey: getBudgetPeriodsQueryKey(options),
  });
};

const createInfiniteParams = <K extends Pick<QueryKey<Options>[0], "body" | "headers" | "path" | "query">>(
  queryKey: QueryKey<Options>,
  page: K
) => {
  const params = queryKey[0];
  if (page.body) {
    params.body = {
      ...(queryKey[0].body as any),
      ...(page.body as any),
    };
  }
  if (page.headers) {
    params.headers = {
      ...queryKey[0].headers,
      ...page.headers,
    };
  }
  if (page.path) {
    params.path = {
      ...(queryKey[0].path as any),
      ...(page.path as any),
    };
  }
  if (page.query) {
    params.query = {
      ...(queryKey[0].query as any),
      ...(page.query as any),
    };
  }
  return params as unknown as typeof page;
};

export const getBudgetPeriodsInfiniteQueryKey = (
  options: Options<GetBudgetPeriodsData>
): QueryKey<Options<GetBudgetPeriodsData>> => createQueryKey("getBudgetPeriods", options, true);

export const getBudgetPeriodsInfiniteOptions = (options: Options<GetBudgetPeriodsData>) => {
  return infiniteQueryOptions<
    GetBudgetPeriodsResponse,
    AxiosError<GetBudgetPeriodsError>,
    InfiniteData<GetBudgetPeriodsResponse>,
    QueryKey<Options<GetBudgetPeriodsData>>,
    number | Pick<QueryKey<Options<GetBudgetPeriodsData>>[0], "body" | "headers" | "path" | "query">
  >(
    // @ts-ignore
    {
      queryFn: async ({ pageParam, queryKey, signal }) => {
        // @ts-ignore
        const page: Pick<QueryKey<Options<GetBudgetPeriodsData>>[0], "body" | "headers" | "path" | "query"> =
          typeof pageParam === "object"
            ? pageParam
            : {
                query: {
                  page: pageParam,
                },
              };
        const params = createInfiniteParams(queryKey, page);
        const { data } = await getBudgetPeriods({
          ...options,
          ...params,
          signal,
          throwOnError: true,
        });
        return data;
      },
      queryKey: getBudgetPeriodsInfiniteQueryKey(options),
    }
  );
};

export const updateBudgetAmountQueryKey = (options: Options<UpdateBudgetAmountData>) =>
  createQueryKey("updateBudgetAmount", options);

export const updateBudgetAmountOptions = (options: Options<UpdateBudgetAmountData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await updateBudgetAmount({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true,
      });
      return data;
    },
    queryKey: updateBudgetAmountQueryKey(options),
  });
};

export const updateBudgetAmountMutation = (options?: Partial<Options<UpdateBudgetAmountData>>) => {
  const mutationOptions: UseMutationOptions<
    UpdateBudgetAmountResponse,
    AxiosError<UpdateBudgetAmountError>,
    Options<UpdateBudgetAmountData>
  > = {
    mutationFn: async (localOptions) => {
      const { data } = await updateBudgetAmount({
        ...options,
        ...localOptions,
        throwOnError: true,
      });
      return data;
    },
  };
  return mutationOptions;
};

export const getCategoriesQueryKey = (options?: Options<GetCategoriesData>) => createQueryKey("getCategories", options);

export const getCategoriesOptions = (options?: Options<GetCategoriesData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getCategories({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true,
      });
      return data;
    },
    queryKey: getCategoriesQueryKey(options),
  });
};

export const createCategoryQueryKey = (options?: Options<CreateCategoryData>) =>
  createQueryKey("createCategory", options);

export const createCategoryOptions = (options?: Options<CreateCategoryData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await createCategory({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true,
      });
      return data;
    },
    queryKey: createCategoryQueryKey(options),
  });
};

export const createCategoryMutation = (options?: Partial<Options<CreateCategoryData>>) => {
  const mutationOptions: UseMutationOptions<
    CreateCategoryResponse,
    AxiosError<CreateCategoryError>,
    Options<CreateCategoryData>
  > = {
    mutationFn: async (localOptions) => {
      const { data } = await createCategory({
        ...options,
        ...localOptions,
        throwOnError: true,
      });
      return data;
    },
  };
  return mutationOptions;
};

export const removeCategoryMutation = (options?: Partial<Options<RemoveCategoryData>>) => {
  const mutationOptions: UseMutationOptions<
    RemoveCategoryResponse,
    AxiosError<RemoveCategoryError>,
    Options<RemoveCategoryData>
  > = {
    mutationFn: async (localOptions) => {
      const { data } = await removeCategory({
        ...options,
        ...localOptions,
        throwOnError: true,
      });
      return data;
    },
  };
  return mutationOptions;
};

export const getCategoryQueryKey = (options: Options<GetCategoryData>) => createQueryKey("getCategory", options);

export const getCategoryOptions = (options: Options<GetCategoryData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getCategory({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true,
      });
      return data;
    },
    queryKey: getCategoryQueryKey(options),
  });
};

export const updateCategoryMutation = (options?: Partial<Options<UpdateCategoryData>>) => {
  const mutationOptions: UseMutationOptions<
    UpdateCategoryResponse,
    AxiosError<UpdateCategoryError>,
    Options<UpdateCategoryData>
  > = {
    mutationFn: async (localOptions) => {
      const { data } = await updateCategory({
        ...options,
        ...localOptions,
        throwOnError: true,
      });
      return data;
    },
  };
  return mutationOptions;
};

export const getFinancialSummaryQueryKey = (options?: Options<GetFinancialSummaryData>) =>
  createQueryKey("getFinancialSummary", options);

export const getFinancialSummaryOptions = (options?: Options<GetFinancialSummaryData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getFinancialSummary({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true,
      });
      return data;
    },
    queryKey: getFinancialSummaryQueryKey(options),
  });
};

export const getSavingsGoalsQueryKey = (options?: Options<GetSavingsGoalsData>) =>
  createQueryKey("getSavingsGoals", options);

export const getSavingsGoalsOptions = (options?: Options<GetSavingsGoalsData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getSavingsGoals({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true,
      });
      return data;
    },
    queryKey: getSavingsGoalsQueryKey(options),
  });
};

export const createSavingsGoalQueryKey = (options?: Options<CreateSavingsGoalData>) =>
  createQueryKey("createSavingsGoal", options);

export const createSavingsGoalOptions = (options?: Options<CreateSavingsGoalData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await createSavingsGoal({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true,
      });
      return data;
    },
    queryKey: createSavingsGoalQueryKey(options),
  });
};

export const createSavingsGoalMutation = (options?: Partial<Options<CreateSavingsGoalData>>) => {
  const mutationOptions: UseMutationOptions<
    CreateSavingsGoalResponse,
    AxiosError<CreateSavingsGoalError>,
    Options<CreateSavingsGoalData>
  > = {
    mutationFn: async (localOptions) => {
      const { data } = await createSavingsGoal({
        ...options,
        ...localOptions,
        throwOnError: true,
      });
      return data;
    },
  };
  return mutationOptions;
};

export const removeSavingsGoalMutation = (options?: Partial<Options<RemoveSavingsGoalData>>) => {
  const mutationOptions: UseMutationOptions<
    RemoveSavingsGoalResponse,
    AxiosError<RemoveSavingsGoalError>,
    Options<RemoveSavingsGoalData>
  > = {
    mutationFn: async (localOptions) => {
      const { data } = await removeSavingsGoal({
        ...options,
        ...localOptions,
        throwOnError: true,
      });
      return data;
    },
  };
  return mutationOptions;
};

export const getSavingsGoalQueryKey = (options: Options<GetSavingsGoalData>) =>
  createQueryKey("getSavingsGoal", options);

export const getSavingsGoalOptions = (options: Options<GetSavingsGoalData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getSavingsGoal({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true,
      });
      return data;
    },
    queryKey: getSavingsGoalQueryKey(options),
  });
};

export const updateSavingsGoalMutation = (options?: Partial<Options<UpdateSavingsGoalData>>) => {
  const mutationOptions: UseMutationOptions<
    UpdateSavingsGoalResponse,
    AxiosError<UpdateSavingsGoalError>,
    Options<UpdateSavingsGoalData>
  > = {
    mutationFn: async (localOptions) => {
      const { data } = await updateSavingsGoal({
        ...options,
        ...localOptions,
        throwOnError: true,
      });
      return data;
    },
  };
  return mutationOptions;
};

export const updateSavingsGoalAccountsMutation = (options?: Partial<Options<UpdateSavingsGoalAccountsData>>) => {
  const mutationOptions: UseMutationOptions<
    UpdateSavingsGoalAccountsResponse,
    AxiosError<UpdateSavingsGoalAccountsError>,
    Options<UpdateSavingsGoalAccountsData>
  > = {
    mutationFn: async (localOptions) => {
      const { data } = await updateSavingsGoalAccounts({
        ...options,
        ...localOptions,
        throwOnError: true,
      });
      return data;
    },
  };
  return mutationOptions;
};

export const updateSavingsGoalStatusMutation = (options?: Partial<Options<UpdateSavingsGoalStatusData>>) => {
  const mutationOptions: UseMutationOptions<
    UpdateSavingsGoalStatusResponse,
    AxiosError<UpdateSavingsGoalStatusError>,
    Options<UpdateSavingsGoalStatusData>
  > = {
    mutationFn: async (localOptions) => {
      const { data } = await updateSavingsGoalStatus({
        ...options,
        ...localOptions,
        throwOnError: true,
      });
      return data;
    },
  };
  return mutationOptions;
};

export const getTasksQueryKey = (options?: Options<GetTasksData>) => createQueryKey("getTasks", options);

export const getTasksOptions = (options?: Options<GetTasksData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getTasks({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true,
      });
      return data;
    },
    queryKey: getTasksQueryKey(options),
  });
};

export const getTasksInfiniteQueryKey = (options?: Options<GetTasksData>): QueryKey<Options<GetTasksData>> =>
  createQueryKey("getTasks", options, true);

export const getTasksInfiniteOptions = (options?: Options<GetTasksData>) => {
  return infiniteQueryOptions<
    GetTasksResponse,
    AxiosError<GetTasksError>,
    InfiniteData<GetTasksResponse>,
    QueryKey<Options<GetTasksData>>,
    number | Pick<QueryKey<Options<GetTasksData>>[0], "body" | "headers" | "path" | "query">
  >(
    // @ts-ignore
    {
      queryFn: async ({ pageParam, queryKey, signal }) => {
        // @ts-ignore
        const page: Pick<QueryKey<Options<GetTasksData>>[0], "body" | "headers" | "path" | "query"> =
          typeof pageParam === "object"
            ? pageParam
            : {
                query: {
                  page: pageParam,
                },
              };
        const params = createInfiniteParams(queryKey, page);
        const { data } = await getTasks({
          ...options,
          ...params,
          signal,
          throwOnError: true,
        });
        return data;
      },
      queryKey: getTasksInfiniteQueryKey(options),
    }
  );
};

export const createTaskQueryKey = (options?: Options<CreateTaskData>) => createQueryKey("createTask", options);

export const createTaskOptions = (options?: Options<CreateTaskData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await createTask({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true,
      });
      return data;
    },
    queryKey: createTaskQueryKey(options),
  });
};

export const createTaskMutation = (options?: Partial<Options<CreateTaskData>>) => {
  const mutationOptions: UseMutationOptions<
    CreateTaskResponse,
    AxiosError<CreateTaskError>,
    Options<CreateTaskData>
  > = {
    mutationFn: async (localOptions) => {
      const { data } = await createTask({
        ...options,
        ...localOptions,
        throwOnError: true,
      });
      return data;
    },
  };
  return mutationOptions;
};

export const getOverdueTasksQueryKey = (options?: Options<GetOverdueTasksData>) =>
  createQueryKey("getOverdueTasks", options);

export const getOverdueTasksOptions = (options?: Options<GetOverdueTasksData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getOverdueTasks({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true,
      });
      return data;
    },
    queryKey: getOverdueTasksQueryKey(options),
  });
};

export const getUpcomingTasksQueryKey = (options?: Options<GetUpcomingTasksData>) =>
  createQueryKey("getUpcomingTasks", options);

export const getUpcomingTasksOptions = (options?: Options<GetUpcomingTasksData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getUpcomingTasks({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true,
      });
      return data;
    },
    queryKey: getUpcomingTasksQueryKey(options),
  });
};

export const deleteTaskMutation = (options?: Partial<Options<DeleteTaskData>>) => {
  const mutationOptions: UseMutationOptions<
    DeleteTaskResponse,
    AxiosError<DeleteTaskError>,
    Options<DeleteTaskData>
  > = {
    mutationFn: async (localOptions) => {
      const { data } = await deleteTask({
        ...options,
        ...localOptions,
        throwOnError: true,
      });
      return data;
    },
  };
  return mutationOptions;
};

export const getTaskQueryKey = (options: Options<GetTaskData>) => createQueryKey("getTask", options);

export const getTaskOptions = (options: Options<GetTaskData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getTask({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true,
      });
      return data;
    },
    queryKey: getTaskQueryKey(options),
  });
};

export const updateTaskMutation = (options?: Partial<Options<UpdateTaskData>>) => {
  const mutationOptions: UseMutationOptions<
    UpdateTaskResponse,
    AxiosError<UpdateTaskError>,
    Options<UpdateTaskData>
  > = {
    mutationFn: async (localOptions) => {
      const { data } = await updateTask({
        ...options,
        ...localOptions,
        throwOnError: true,
      });
      return data;
    },
  };
  return mutationOptions;
};

export const completeTaskMutation = (options?: Partial<Options<CompleteTaskData>>) => {
  const mutationOptions: UseMutationOptions<
    CompleteTaskResponse,
    AxiosError<CompleteTaskError>,
    Options<CompleteTaskData>
  > = {
    mutationFn: async (localOptions) => {
      const { data } = await completeTask({
        ...options,
        ...localOptions,
        throwOnError: true,
      });
      return data;
    },
  };
  return mutationOptions;
};

export const getTaskRecordsQueryKey = (options: Options<GetTaskRecordsData>) =>
  createQueryKey("getTaskRecords", options);

export const getTaskRecordsOptions = (options: Options<GetTaskRecordsData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getTaskRecords({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true,
      });
      return data;
    },
    queryKey: getTaskRecordsQueryKey(options),
  });
};

export const getTaskRecordsInfiniteQueryKey = (
  options: Options<GetTaskRecordsData>
): QueryKey<Options<GetTaskRecordsData>> => createQueryKey("getTaskRecords", options, true);

export const getTaskRecordsInfiniteOptions = (options: Options<GetTaskRecordsData>) => {
  return infiniteQueryOptions<
    GetTaskRecordsResponse,
    AxiosError<GetTaskRecordsError>,
    InfiniteData<GetTaskRecordsResponse>,
    QueryKey<Options<GetTaskRecordsData>>,
    number | Pick<QueryKey<Options<GetTaskRecordsData>>[0], "body" | "headers" | "path" | "query">
  >(
    // @ts-ignore
    {
      queryFn: async ({ pageParam, queryKey, signal }) => {
        // @ts-ignore
        const page: Pick<QueryKey<Options<GetTaskRecordsData>>[0], "body" | "headers" | "path" | "query"> =
          typeof pageParam === "object"
            ? pageParam
            : {
                query: {
                  page: pageParam,
                },
              };
        const params = createInfiniteParams(queryKey, page);
        const { data } = await getTaskRecords({
          ...options,
          ...params,
          signal,
          throwOnError: true,
        });
        return data;
      },
      queryKey: getTaskRecordsInfiniteQueryKey(options),
    }
  );
};

export const getTransactionsQueryKey = (options?: Options<GetTransactionsData>) =>
  createQueryKey("getTransactions", options);

export const getTransactionsOptions = (options?: Options<GetTransactionsData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getTransactions({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true,
      });
      return data;
    },
    queryKey: getTransactionsQueryKey(options),
  });
};

export const getTransactionsInfiniteQueryKey = (
  options?: Options<GetTransactionsData>
): QueryKey<Options<GetTransactionsData>> => createQueryKey("getTransactions", options, true);

export const getTransactionsInfiniteOptions = (options?: Options<GetTransactionsData>) => {
  return infiniteQueryOptions<
    GetTransactionsResponse,
    AxiosError<GetTransactionsError>,
    InfiniteData<GetTransactionsResponse>,
    QueryKey<Options<GetTransactionsData>>,
    number | Pick<QueryKey<Options<GetTransactionsData>>[0], "body" | "headers" | "path" | "query">
  >(
    // @ts-ignore
    {
      queryFn: async ({ pageParam, queryKey, signal }) => {
        // @ts-ignore
        const page: Pick<QueryKey<Options<GetTransactionsData>>[0], "body" | "headers" | "path" | "query"> =
          typeof pageParam === "object"
            ? pageParam
            : {
                query: {
                  page: pageParam,
                },
              };
        const params = createInfiniteParams(queryKey, page);
        const { data } = await getTransactions({
          ...options,
          ...params,
          signal,
          throwOnError: true,
        });
        return data;
      },
      queryKey: getTransactionsInfiniteQueryKey(options),
    }
  );
};

export const createTransactionQueryKey = (options?: Options<CreateTransactionData>) =>
  createQueryKey("createTransaction", options);

export const createTransactionOptions = (options?: Options<CreateTransactionData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await createTransaction({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true,
      });
      return data;
    },
    queryKey: createTransactionQueryKey(options),
  });
};

export const createTransactionMutation = (options?: Partial<Options<CreateTransactionData>>) => {
  const mutationOptions: UseMutationOptions<
    CreateTransactionResponse,
    AxiosError<CreateTransactionError>,
    Options<CreateTransactionData>
  > = {
    mutationFn: async (localOptions) => {
      const { data } = await createTransaction({
        ...options,
        ...localOptions,
        throwOnError: true,
      });
      return data;
    },
  };
  return mutationOptions;
};

export const removeTransactionMutation = (options?: Partial<Options<RemoveTransactionData>>) => {
  const mutationOptions: UseMutationOptions<
    RemoveTransactionResponse,
    AxiosError<RemoveTransactionError>,
    Options<RemoveTransactionData>
  > = {
    mutationFn: async (localOptions) => {
      const { data } = await removeTransaction({
        ...options,
        ...localOptions,
        throwOnError: true,
      });
      return data;
    },
  };
  return mutationOptions;
};

export const getTransactionQueryKey = (options: Options<GetTransactionData>) =>
  createQueryKey("getTransaction", options);

export const getTransactionOptions = (options: Options<GetTransactionData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await getTransaction({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true,
      });
      return data;
    },
    queryKey: getTransactionQueryKey(options),
  });
};

export const updateTransactionMutation = (options?: Partial<Options<UpdateTransactionData>>) => {
  const mutationOptions: UseMutationOptions<
    UpdateTransactionResponse,
    AxiosError<UpdateTransactionError>,
    Options<UpdateTransactionData>
  > = {
    mutationFn: async (localOptions) => {
      const { data } = await updateTransaction({
        ...options,
        ...localOptions,
        throwOnError: true,
      });
      return data;
    },
  };
  return mutationOptions;
};

export const createUserQueryKey = (options?: Options<CreateUserData>) => createQueryKey("createUser", options);

export const createUserOptions = (options?: Options<CreateUserData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await createUser({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true,
      });
      return data;
    },
    queryKey: createUserQueryKey(options),
  });
};

export const createUserMutation = (options?: Partial<Options<CreateUserData>>) => {
  const mutationOptions: UseMutationOptions<
    CreateUserResponse,
    AxiosError<CreateUserError>,
    Options<CreateUserData>
  > = {
    mutationFn: async (localOptions) => {
      const { data } = await createUser({
        ...options,
        ...localOptions,
        throwOnError: true,
      });
      return data;
    },
  };
  return mutationOptions;
};

export const currentUserQueryKey = (options?: Options<CurrentUserData>) => createQueryKey("currentUser", options);

export const currentUserOptions = (options?: Options<CurrentUserData>) => {
  return queryOptions({
    queryFn: async ({ queryKey, signal }) => {
      const { data } = await currentUser({
        ...options,
        ...queryKey[0],
        signal,
        throwOnError: true,
      });
      return data;
    },
    queryKey: currentUserQueryKey(options),
  });
};
