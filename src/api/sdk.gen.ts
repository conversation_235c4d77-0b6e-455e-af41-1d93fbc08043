// This file is auto-generated by @hey-api/openapi-ts

import type { Client, Options as ClientOptions, TDataShape } from "@hey-api/client-axios";
import type {
  CompleteTaskData,
  CompleteTaskError,
  CompleteTaskResponse,
  CreateAccountData,
  CreateAccountError,
  CreateAccountResponse,
  CreateBudgetData,
  CreateBudgetError,
  CreateBudgetResponse,
  CreateCategoryData,
  CreateCategoryError,
  CreateCategoryResponse,
  CreateSavingsGoalData,
  CreateSavingsGoalError,
  CreateSavingsGoalResponse,
  CreateTaskData,
  CreateTaskError,
  CreateTaskResponse,
  CreateTransactionData,
  CreateTransactionError,
  CreateTransactionResponse,
  CreateUserData,
  CreateUserError,
  CreateUserResponse,
  CurrentUserData,
  CurrentUserError,
  CurrentUserResponse,
  DeleteBudgetData,
  DeleteBudgetError,
  DeleteBudgetResponse,
  DeleteTaskData,
  DeleteTaskError,
  DeleteTaskResponse,
  GetAccountData,
  GetAccountError,
  GetAccountResponse,
  GetAccountsData,
  GetAccountsError,
  GetAccountsResponse,
  GetBudgetData,
  GetBudgetError,
  GetBudgetPeriodsData,
  GetBudgetPeriodsError,
  GetBudgetPeriodsResponse,
  GetBudgetResponse,
  GetBudgetsData,
  GetBudgetsError,
  GetBudgetsResponse,
  GetCategoriesData,
  GetCategoriesError,
  GetCategoriesResponse,
  GetCategoryData,
  GetCategoryError,
  GetCategoryResponse,
  GetFinancialSummaryData,
  GetFinancialSummaryError,
  GetFinancialSummaryResponse,
  GetOverdueTasksData,
  GetOverdueTasksError,
  GetOverdueTasksResponse,
  GetSavingsGoalData,
  GetSavingsGoalError,
  GetSavingsGoalResponse,
  GetSavingsGoalsData,
  GetSavingsGoalsError,
  GetSavingsGoalsResponse,
  GetTaskData,
  GetTaskError,
  GetTaskRecordsData,
  GetTaskRecordsError,
  GetTaskRecordsResponse,
  GetTaskResponse,
  GetTasksData,
  GetTasksError,
  GetTasksResponse,
  GetTokenData,
  GetTokenError,
  GetTokenResponse,
  GetTransactionData,
  GetTransactionError,
  GetTransactionResponse,
  GetTransactionsData,
  GetTransactionsError,
  GetTransactionsResponse,
  GetUpcomingTasksData,
  GetUpcomingTasksError,
  GetUpcomingTasksResponse,
  LogoutData,
  LogoutError,
  LogoutResponse,
  RemoveAccountData,
  RemoveAccountError,
  RemoveAccountResponse,
  RemoveCategoryData,
  RemoveCategoryError,
  RemoveCategoryResponse,
  RemoveSavingsGoalData,
  RemoveSavingsGoalError,
  RemoveSavingsGoalResponse,
  RemoveTransactionData,
  RemoveTransactionError,
  RemoveTransactionResponse,
  RequestPasswordResetData,
  RequestPasswordResetError,
  RequestPasswordResetResponse,
  ResetPasswordData,
  ResetPasswordError,
  ResetPasswordResponse,
  SetAccountBalanceData,
  SetAccountBalanceError,
  SetAccountBalanceResponse,
  UpdateAccountData,
  UpdateAccountError,
  UpdateAccountResponse,
  UpdateBudgetAmountData,
  UpdateBudgetAmountError,
  UpdateBudgetAmountResponse,
  UpdateBudgetData,
  UpdateBudgetError,
  UpdateBudgetResponse,
  UpdateCategoryData,
  UpdateCategoryError,
  UpdateCategoryResponse,
  UpdateSavingsGoalAccountsData,
  UpdateSavingsGoalAccountsError,
  UpdateSavingsGoalAccountsResponse,
  UpdateSavingsGoalData,
  UpdateSavingsGoalError,
  UpdateSavingsGoalResponse,
  UpdateSavingsGoalStatusData,
  UpdateSavingsGoalStatusError,
  UpdateSavingsGoalStatusResponse,
  UpdateTaskData,
  UpdateTaskError,
  UpdateTaskResponse,
  UpdateTransactionData,
  UpdateTransactionError,
  UpdateTransactionResponse,
} from "./types.gen";

import { client as _heyApiClient } from "./client.gen";

export type Options<TData extends TDataShape = TDataShape, ThrowOnError extends boolean = boolean> = ClientOptions<
  TData,
  ThrowOnError
> & {
  /**
   * You can provide a client instance returned by `createClient()` instead of
   * individual options. This might be also useful if you want to implement a
   * custom client.
   */
  client?: Client;
  /**
   * You can pass arbitrary values through the `meta` object. This can be
   * used to access values that aren't defined as part of the SDK function.
   */
  meta?: Record<string, unknown>;
};

/**
 * Get all accounts
 * Get all user accounts
 */
export const getAccounts = <ThrowOnError extends boolean = false>(options?: Options<GetAccountsData, ThrowOnError>) => {
  return (options?.client ?? _heyApiClient).get<GetAccountsResponse, GetAccountsError, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/accounts",
    ...options,
  });
};

/**
 * Create account
 * Create a new account
 */
export const createAccount = <ThrowOnError extends boolean = false>(
  options?: Options<CreateAccountData, ThrowOnError>
) => {
  return (options?.client ?? _heyApiClient).post<CreateAccountResponse, CreateAccountError, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/accounts",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

/**
 * Remove account
 * Remove account by id
 */
export const removeAccount = <ThrowOnError extends boolean = false>(
  options: Options<RemoveAccountData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).delete<RemoveAccountResponse, RemoveAccountError, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/accounts/{account_id}",
    ...options,
  });
};

/**
 * Get account
 * Get account by id
 */
export const getAccount = <ThrowOnError extends boolean = false>(options: Options<GetAccountData, ThrowOnError>) => {
  return (options.client ?? _heyApiClient).get<GetAccountResponse, GetAccountError, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/accounts/{account_id}",
    ...options,
  });
};

/**
 * Update account
 * Update account by id
 */
export const updateAccount = <ThrowOnError extends boolean = false>(
  options: Options<UpdateAccountData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).patch<UpdateAccountResponse, UpdateAccountError, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/accounts/{account_id}",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

/**
 * Set account balance
 * Set account balance and create a transaction if balance changes
 */
export const setAccountBalance = <ThrowOnError extends boolean = false>(
  options: Options<SetAccountBalanceData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).post<SetAccountBalanceResponse, SetAccountBalanceError, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/accounts/{account_id}/set-balance",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

/**
 * Logout
 * Logout user, remove access token and csrf cookies
 */
export const logout = <ThrowOnError extends boolean = false>(options?: Options<LogoutData, ThrowOnError>) => {
  return (options?.client ?? _heyApiClient).delete<LogoutResponse, LogoutError, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/auth/logout",
    ...options,
  });
};

/**
 * Request password reset
 * Request a password reset email. If the email exists, a reset link will be sent.
 */
export const requestPasswordReset = <ThrowOnError extends boolean = false>(
  options?: Options<RequestPasswordResetData, ThrowOnError>
) => {
  return (options?.client ?? _heyApiClient).post<RequestPasswordResetResponse, RequestPasswordResetError, ThrowOnError>(
    {
      url: "/api/v1/auth/password-reset/request",
      ...options,
      headers: {
        "Content-Type": "application/json",
        ...options?.headers,
      },
    }
  );
};

/**
 * Reset password
 * Reset a password using a reset token
 */
export const resetPassword = <ThrowOnError extends boolean = false>(
  options?: Options<ResetPasswordData, ThrowOnError>
) => {
  return (options?.client ?? _heyApiClient).post<ResetPasswordResponse, ResetPasswordError, ThrowOnError>({
    url: "/api/v1/auth/password-reset/reset",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

/**
 * Get token
 * Get authentication token using email and password
 */
export const getToken = <ThrowOnError extends boolean = false>(options?: Options<GetTokenData, ThrowOnError>) => {
  return (options?.client ?? _heyApiClient).post<GetTokenResponse, GetTokenError, ThrowOnError>({
    url: "/api/v1/auth/token",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

/**
 * Get budgets
 * Get a list of user's budgets
 */
export const getBudgets = <ThrowOnError extends boolean = false>(options?: Options<GetBudgetsData, ThrowOnError>) => {
  return (options?.client ?? _heyApiClient).get<GetBudgetsResponse, GetBudgetsError, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/budgets",
    ...options,
  });
};

/**
 * Create budget
 * Create a new budget
 */
export const createBudget = <ThrowOnError extends boolean = false>(
  options?: Options<CreateBudgetData, ThrowOnError>
) => {
  return (options?.client ?? _heyApiClient).post<CreateBudgetResponse, CreateBudgetError, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/budgets",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

/**
 * Delete budget
 * Delete a budget
 */
export const deleteBudget = <ThrowOnError extends boolean = false>(
  options: Options<DeleteBudgetData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).delete<DeleteBudgetResponse, DeleteBudgetError, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/budgets/{budget_id}",
    ...options,
  });
};

/**
 * Get budget
 * Get a budget by ID
 */
export const getBudget = <ThrowOnError extends boolean = false>(options: Options<GetBudgetData, ThrowOnError>) => {
  return (options.client ?? _heyApiClient).get<GetBudgetResponse, GetBudgetError, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/budgets/{budget_id}",
    ...options,
  });
};

/**
 * Update budget
 * Update a budget
 */
export const updateBudget = <ThrowOnError extends boolean = false>(
  options: Options<UpdateBudgetData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).patch<UpdateBudgetResponse, UpdateBudgetError, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/budgets/{budget_id}",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

/**
 * Get budget periods
 * Get a paginated list of budget periods
 */
export const getBudgetPeriods = <ThrowOnError extends boolean = false>(
  options: Options<GetBudgetPeriodsData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).get<GetBudgetPeriodsResponse, GetBudgetPeriodsError, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/budgets/{budget_id}/periods",
    ...options,
  });
};

/**
 * Update budget amount
 * Update the current amount for the active budget period
 */
export const updateBudgetAmount = <ThrowOnError extends boolean = false>(
  options: Options<UpdateBudgetAmountData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).post<UpdateBudgetAmountResponse, UpdateBudgetAmountError, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/budgets/{budget_id}/update-amount",
    ...options,
  });
};

/**
 * Get all categories
 * Get all user categories
 */
export const getCategories = <ThrowOnError extends boolean = false>(
  options?: Options<GetCategoriesData, ThrowOnError>
) => {
  return (options?.client ?? _heyApiClient).get<GetCategoriesResponse, GetCategoriesError, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/categories",
    ...options,
  });
};

/**
 * Create category
 * Create a new category
 */
export const createCategory = <ThrowOnError extends boolean = false>(
  options?: Options<CreateCategoryData, ThrowOnError>
) => {
  return (options?.client ?? _heyApiClient).post<CreateCategoryResponse, CreateCategoryError, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/categories",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

/**
 * Remove category
 * Remove category by id
 */
export const removeCategory = <ThrowOnError extends boolean = false>(
  options: Options<RemoveCategoryData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).delete<RemoveCategoryResponse, RemoveCategoryError, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/categories/{category_id}",
    ...options,
  });
};

/**
 * Get category
 * Get category by id
 */
export const getCategory = <ThrowOnError extends boolean = false>(options: Options<GetCategoryData, ThrowOnError>) => {
  return (options.client ?? _heyApiClient).get<GetCategoryResponse, GetCategoryError, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/categories/{category_id}",
    ...options,
  });
};

/**
 * Update category
 * Update category by id
 */
export const updateCategory = <ThrowOnError extends boolean = false>(
  options: Options<UpdateCategoryData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).patch<UpdateCategoryResponse, UpdateCategoryError, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/categories/{category_id}",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

/**
 * Get financial summary
 * Get a summary of user's financials including total balance, current month and previous month totals
 */
export const getFinancialSummary = <ThrowOnError extends boolean = false>(
  options?: Options<GetFinancialSummaryData, ThrowOnError>
) => {
  return (options?.client ?? _heyApiClient).get<GetFinancialSummaryResponse, GetFinancialSummaryError, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/reports/summary",
    ...options,
  });
};

/**
 * Get all savings goals
 * Get all user savings goals
 */
export const getSavingsGoals = <ThrowOnError extends boolean = false>(
  options?: Options<GetSavingsGoalsData, ThrowOnError>
) => {
  return (options?.client ?? _heyApiClient).get<GetSavingsGoalsResponse, GetSavingsGoalsError, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/savings/goals",
    ...options,
  });
};

/**
 * Create savings goal
 * Create a new savings goal
 */
export const createSavingsGoal = <ThrowOnError extends boolean = false>(
  options?: Options<CreateSavingsGoalData, ThrowOnError>
) => {
  return (options?.client ?? _heyApiClient).post<CreateSavingsGoalResponse, CreateSavingsGoalError, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/savings/goals",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

/**
 * Remove savings goal
 * Remove savings goal by id
 */
export const removeSavingsGoal = <ThrowOnError extends boolean = false>(
  options: Options<RemoveSavingsGoalData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).delete<RemoveSavingsGoalResponse, RemoveSavingsGoalError, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/savings/goals/{goal_id}",
    ...options,
  });
};

/**
 * Get savings goal
 * Get savings goal by id
 */
export const getSavingsGoal = <ThrowOnError extends boolean = false>(
  options: Options<GetSavingsGoalData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).get<GetSavingsGoalResponse, GetSavingsGoalError, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/savings/goals/{goal_id}",
    ...options,
  });
};

/**
 * Update savings goal
 * Update savings goal by id
 */
export const updateSavingsGoal = <ThrowOnError extends boolean = false>(
  options: Options<UpdateSavingsGoalData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).patch<UpdateSavingsGoalResponse, UpdateSavingsGoalError, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/savings/goals/{goal_id}",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

/**
 * Update savings goal accounts
 * Update accounts associated with a savings goal
 */
export const updateSavingsGoalAccounts = <ThrowOnError extends boolean = false>(
  options: Options<UpdateSavingsGoalAccountsData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).patch<
    UpdateSavingsGoalAccountsResponse,
    UpdateSavingsGoalAccountsError,
    ThrowOnError
  >({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/savings/goals/{goal_id}/accounts",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

/**
 * Update savings goal status
 * Update savings goal status (active, completed, archived)
 */
export const updateSavingsGoalStatus = <ThrowOnError extends boolean = false>(
  options: Options<UpdateSavingsGoalStatusData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).patch<
    UpdateSavingsGoalStatusResponse,
    UpdateSavingsGoalStatusError,
    ThrowOnError
  >({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/savings/goals/{goal_id}/status/{status}",
    ...options,
  });
};

/**
 * Get tasks
 * Get a list of user's tasks
 */
export const getTasks = <ThrowOnError extends boolean = false>(options?: Options<GetTasksData, ThrowOnError>) => {
  return (options?.client ?? _heyApiClient).get<GetTasksResponse, GetTasksError, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/tasks",
    ...options,
  });
};

/**
 * Create task
 * Create a new task
 */
export const createTask = <ThrowOnError extends boolean = false>(options?: Options<CreateTaskData, ThrowOnError>) => {
  return (options?.client ?? _heyApiClient).post<CreateTaskResponse, CreateTaskError, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/tasks",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

/**
 * Get overdue tasks
 * Get tasks that are overdue (period ended but not completed)
 */
export const getOverdueTasks = <ThrowOnError extends boolean = false>(
  options?: Options<GetOverdueTasksData, ThrowOnError>
) => {
  return (options?.client ?? _heyApiClient).get<GetOverdueTasksResponse, GetOverdueTasksError, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/tasks/overdue",
    ...options,
  });
};

/**
 * Get upcoming tasks
 * Get tasks that are due in the next 7 days
 */
export const getUpcomingTasks = <ThrowOnError extends boolean = false>(
  options?: Options<GetUpcomingTasksData, ThrowOnError>
) => {
  return (options?.client ?? _heyApiClient).get<GetUpcomingTasksResponse, GetUpcomingTasksError, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/tasks/upcoming",
    ...options,
  });
};

/**
 * Delete task
 * Delete a task
 */
export const deleteTask = <ThrowOnError extends boolean = false>(options: Options<DeleteTaskData, ThrowOnError>) => {
  return (options.client ?? _heyApiClient).delete<DeleteTaskResponse, DeleteTaskError, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/tasks/{task_id}",
    ...options,
  });
};

/**
 * Get task
 * Get a task by ID
 */
export const getTask = <ThrowOnError extends boolean = false>(options: Options<GetTaskData, ThrowOnError>) => {
  return (options.client ?? _heyApiClient).get<GetTaskResponse, GetTaskError, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/tasks/{task_id}",
    ...options,
  });
};

/**
 * Update task
 * Update a task
 */
export const updateTask = <ThrowOnError extends boolean = false>(options: Options<UpdateTaskData, ThrowOnError>) => {
  return (options.client ?? _heyApiClient).patch<UpdateTaskResponse, UpdateTaskError, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/tasks/{task_id}",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

/**
 * Complete task
 * Mark a task as complete or incomplete for the current period
 */
export const completeTask = <ThrowOnError extends boolean = false>(
  options: Options<CompleteTaskData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).patch<CompleteTaskResponse, CompleteTaskError, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/tasks/{task_id}/complete",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

/**
 * Get task records
 * Get a paginated list of task records
 */
export const getTaskRecords = <ThrowOnError extends boolean = false>(
  options: Options<GetTaskRecordsData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).get<GetTaskRecordsResponse, GetTaskRecordsError, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/tasks/{task_id}/records",
    ...options,
  });
};

/**
 * Get transactions
 * Get paginated list of transactions for current user
 */
export const getTransactions = <ThrowOnError extends boolean = false>(
  options?: Options<GetTransactionsData, ThrowOnError>
) => {
  return (options?.client ?? _heyApiClient).get<GetTransactionsResponse, GetTransactionsError, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/transactions",
    ...options,
  });
};

/**
 * Create transaction
 * Create a new transaction
 */
export const createTransaction = <ThrowOnError extends boolean = false>(
  options?: Options<CreateTransactionData, ThrowOnError>
) => {
  return (options?.client ?? _heyApiClient).post<CreateTransactionResponse, CreateTransactionError, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/transactions",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

/**
 * Remove transaction
 * Remove transaction by id
 */
export const removeTransaction = <ThrowOnError extends boolean = false>(
  options: Options<RemoveTransactionData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).delete<RemoveTransactionResponse, RemoveTransactionError, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/transactions/{transaction_id}",
    ...options,
  });
};

/**
 * Get transaction
 * Get transaction by id
 */
export const getTransaction = <ThrowOnError extends boolean = false>(
  options: Options<GetTransactionData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).get<GetTransactionResponse, GetTransactionError, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/transactions/{transaction_id}",
    ...options,
  });
};

/**
 * Update transaction
 * Update transaction by id
 */
export const updateTransaction = <ThrowOnError extends boolean = false>(
  options: Options<UpdateTransactionData, ThrowOnError>
) => {
  return (options.client ?? _heyApiClient).patch<UpdateTransactionResponse, UpdateTransactionError, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/transactions/{transaction_id}",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

/**
 * Create user
 * Create (register) a new user
 */
export const createUser = <ThrowOnError extends boolean = false>(options?: Options<CreateUserData, ThrowOnError>) => {
  return (options?.client ?? _heyApiClient).post<CreateUserResponse, CreateUserError, ThrowOnError>({
    url: "/api/v1/users",
    ...options,
    headers: {
      "Content-Type": "application/json",
      ...options?.headers,
    },
  });
};

/**
 * Current user
 * Get currently authenticated (with JWT) user details
 */
export const currentUser = <ThrowOnError extends boolean = false>(options?: Options<CurrentUserData, ThrowOnError>) => {
  return (options?.client ?? _heyApiClient).get<CurrentUserResponse, CurrentUserError, ThrowOnError>({
    security: [
      {
        scheme: "bearer",
        type: "http",
      },
    ],
    url: "/api/v1/users/me",
    ...options,
  });
};
