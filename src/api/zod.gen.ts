// This file is auto-generated by @hey-api/openapi-ts

import { z } from "zod";

export const zAccount = z.object({
  account_type: z.enum(["cash", "card", "bank_account", "savings", "loan", "other"]),
  base_current_balance: z.string().regex(/^(-|\+)?((\d+(\.\d*)?)|(\.\d+))$/),
  base_opening_balance: z.string().regex(/^(-|\+)?((\d+(\.\d*)?)|(\.\d+))$/),
  color: z.string(),
  created_at: z.string().datetime(),
  currency: z.enum(["EUR", "UAH", "USD"]),
  current_balance: z.string().regex(/^(-|\+)?((\d+(\.\d*)?)|(\.\d+))$/),
  description: z.union([z.string(), z.null()]),
  group: z.union([z.string(), z.null()]),
  id: z.string().uuid(),
  is_active: z.boolean(),
  name: z.string(),
  opening_balance: z.string().regex(/^(-|\+)?((\d+(\.\d*)?)|(\.\d+))$/),
  overdraft_limit: z.string().regex(/^(-|\+)?((\d+(\.\d*)?)|(\.\d+))$/),
  updated_at: z.string().datetime(),
});

export const zAccountCreateRequest = z.object({
  account_type: z.enum(["cash", "card", "bank_account", "savings", "loan", "other"]).optional(),
  color: z.string().optional().default("#6186ff"),
  currency: z.enum(["EUR", "UAH", "USD"]),
  description: z
    .union([z.string().max(300), z.null()])
    .optional()
    .default(null),
  group: z
    .union([z.string().min(3).max(50), z.null()])
    .optional()
    .default(null),
  is_active: z.boolean().optional().default(true),
  name: z.string().min(3).max(50),
  opening_balance: z
    .string()
    .regex(/^(-|\+)?((\d+(\.\d*)?)|(\.\d+))$/)
    .optional(),
  overdraft_limit: z
    .string()
    .regex(/^(-|\+)?((\d+(\.\d*)?)|(\.\d+))$/)
    .optional(),
});

export const zAccountSetBalanceRequest = z.object({
  balance: z.string().regex(/^(-|\+)?((\d+(\.\d*)?)|(\.\d+))$/),
});

export const zAccountUpdateRequest = z.object({
  account_type: z.enum(["cash", "card", "bank_account", "savings", "loan", "other"]).optional(),
  color: z.string().optional().default("#6186ff"),
  currency: z.enum(["EUR", "UAH", "USD"]).optional(),
  description: z
    .union([z.string().max(300), z.null()])
    .optional()
    .default(null),
  group: z
    .union([z.string().min(3).max(50), z.null()])
    .optional()
    .default(null),
  is_active: z.boolean().optional().default(true),
  name: z.string().min(3).max(50).optional(),
  overdraft_limit: z
    .string()
    .regex(/^(-|\+)?((\d+(\.\d*)?)|(\.\d+))$/)
    .optional(),
});

export const zBudget = z.object({
  amount: z.string().regex(/^(-|\+)?((\d+(\.\d*)?)|(\.\d+))$/),
  created_at: z.string().datetime(),
  current_period: z.union([
    z.union([z.object({}), z.null()]),
    z.object({
      budget_id: z.string().uuid(),
      created_at: z.string().datetime(),
      current_amount: z.string().regex(/^(-|\+)?((\d+(\.\d*)?)|(\.\d+))$/),
      end_date: z.string().date(),
      id: z.string().uuid(),
      is_active: z.boolean(),
      planned_amount: z.string().regex(/^(-|\+)?((\d+(\.\d*)?)|(\.\d+))$/),
      start_date: z.string().date(),
      updated_at: z.string().datetime(),
    }),
  ]),
  description: z.union([z.string(), z.null()]),
  id: z.string().uuid(),
  is_active: z.boolean(),
  is_favorite: z.boolean(),
  is_fixed_amount: z.boolean(),
  name: z.string(),
  period_type: z.enum(["week", "month", "year"]),
  updated_at: z.string().datetime(),
});

export const zBudgetPeriod = z.object({
  budget_id: z.string().uuid(),
  created_at: z.string().datetime(),
  current_amount: z.string().regex(/^(-|\+)?((\d+(\.\d*)?)|(\.\d+))$/),
  end_date: z.string().date(),
  id: z.string().uuid(),
  is_active: z.boolean(),
  planned_amount: z.string().regex(/^(-|\+)?((\d+(\.\d*)?)|(\.\d+))$/),
  start_date: z.string().date(),
  updated_at: z.string().datetime(),
});

export const zBudgetPeriodsList = z.object({
  items: z.array(zBudgetPeriod).optional(),
  pagination: z
    .object({
      has_next: z.boolean(),
      has_prev: z.boolean(),
      page: z.number().int(),
      pages: z.number().int(),
      per_page: z.number().int(),
      total: z.number().int(),
    })
    .optional(),
});

export const zBudgetRequest = z.object({
  amount: z.string().regex(/^(-|\+)?((\d+(\.\d*)?)|(\.\d+))$/),
  description: z.union([z.string().max(300), z.null()]).optional(),
  is_active: z.boolean().optional(),
  is_favorite: z.boolean().optional().default(false),
  is_fixed_amount: z.boolean().optional().default(true),
  name: z.string().min(3).max(50),
  period_type: z.enum(["week", "month", "year"]).optional(),
});

export const zCategory = z.object({
  color: z.string(),
  icon: z.union([z.string(), z.null()]),
  id: z.string().uuid(),
  is_expense: z.boolean(),
  name: z.string(),
});

export const zCategoryCreateRequest = z.object({
  color: z.string().optional().default("#129692"),
  icon: z
    .union([z.string().max(50), z.null()])
    .optional()
    .default(null),
  is_expense: z.boolean().optional().default(true),
  name: z.string().min(3).max(50),
});

export const zCategoryUpdateRequest = z.object({
  color: z.string().optional(),
  icon: z
    .union([z.string().max(50), z.null()])
    .optional()
    .default(null),
  is_expense: z.boolean().optional(),
  name: z.string().min(3).max(50).optional(),
});

export const zCompleteTaskRequest = z.object({
  is_completed: z.boolean(),
});

export const zCreateUserRequest = z.object({
  base_currency: z.enum(["EUR", "UAH", "USD"]),
  email: z.string().email(),
  name: z.union([z.string().max(50), z.null()]).optional(),
  password: z.string().min(3),
});

export const zHttpError = z.object({
  detail: z.object({}).optional(),
  message: z.string().optional(),
});

export const zMonthlyTotals = z.object({
  expenses: z.string().regex(/^(-|\+)?((\d+(\.\d*)?)|(\.\d+))$/),
  income: z.string().regex(/^(-|\+)?((\d+(\.\d*)?)|(\.\d+))$/),
});

export const zPagination = z.object({
  has_next: z.boolean(),
  has_prev: z.boolean(),
  page: z.number().int(),
  pages: z.number().int(),
  per_page: z.number().int(),
  total: z.number().int(),
});

export const zPasswordReset = z.object({
  password: z.string(),
  token: z.string(),
});

export const zPasswordResetRequest = z.object({
  email: z.string().email(),
});

export const zPasswordResetResponse = z.object({
  message: z.string(),
});

export const zPasswordResetSuccess = z.object({
  message: z.string(),
});

export const zSavingsGoal = z.object({
  accounts: z.array(zAccount),
  created_at: z.string().datetime(),
  currency: z.enum(["EUR", "UAH", "USD"]),
  current_amount: z.string().regex(/^(-|\+)?((\d+(\.\d*)?)|(\.\d+))$/),
  description: z.union([z.string(), z.null()]),
  id: z.string().uuid(),
  name: z.string(),
  status: z.enum(["active", "completed", "archived"]),
  target_amount: z.union([z.string().regex(/^(-|\+)?((\d+(\.\d*)?)|(\.\d+))$/), z.null()]),
  target_date: z.union([z.string().date(), z.null()]),
  updated_at: z.string().datetime(),
});

export const zSavingsGoalAccountsUpdateRequest = z.object({
  account_ids: z.array(z.string().uuid()).min(1),
});

export const zSavingsGoalCreateRequest = z.object({
  account_ids: z.array(z.string().uuid()).min(1),
  currency: z.enum(["EUR", "UAH", "USD"]),
  description: z
    .union([z.string().max(300), z.null()])
    .optional()
    .default(null),
  name: z.string().min(3).max(50),
  target_amount: z
    .union([z.string().regex(/^(-|\+)?((\d+(\.\d*)?)|(\.\d+))$/), z.null()])
    .optional()
    .default(null),
  target_date: z.union([z.string().date(), z.null()]).optional().default(null),
});

export const zSavingsGoalUpdateRequest = z.object({
  account_ids: z.array(z.string().uuid()).min(1).optional(),
  description: z
    .union([z.string().max(300), z.null()])
    .optional()
    .default(null),
  name: z.string().min(3).max(50).optional(),
  status: z.enum(["active", "completed", "archived"]).optional(),
  target_amount: z
    .union([z.string().regex(/^(-|\+)?((\d+(\.\d*)?)|(\.\d+))$/), z.null()])
    .optional()
    .default(null),
  target_date: z.union([z.string().date(), z.null()]).optional().default(null),
});

export const zSummary = z.object({
  current_month: zMonthlyTotals,
  previous_month: zMonthlyTotals,
  previous_month_end_balance: z.string().regex(/^(-|\+)?((\d+(\.\d*)?)|(\.\d+))$/),
  total_balance: z.string().regex(/^(-|\+)?((\d+(\.\d*)?)|(\.\d+))$/),
});

export const zTask = z.object({
  created_at: z.string().datetime(),
  current_record: z.union([
    z.object({
      completed_at: z.union([z.string().datetime(), z.null()]).optional(),
      created_at: z.string().datetime(),
      end_date: z.string().date(),
      id: z.string().uuid(),
      is_completed: z.boolean(),
      start_date: z.string().date(),
      task_id: z.string().uuid(),
      updated_at: z.string().datetime(),
    }),
    z.union([z.object({}), z.null()]),
    z.null(),
  ]),
  description: z.union([z.string(), z.null()]),
  enable_notifications: z.boolean(),
  id: z.string().uuid(),
  is_active: z.boolean(),
  name: z.string(),
  period_type: z.enum(["week", "month", "quarter", "year"]),
  target_amount: z.union([z.string().regex(/^(-|\+)?((\d+(\.\d*)?)|(\.\d+))$/), z.null()]),
  updated_at: z.string().datetime(),
});

export const zTaskRecord = z.object({
  completed_at: z.union([z.string().datetime(), z.null()]).optional(),
  created_at: z.string().datetime(),
  end_date: z.string().date(),
  id: z.string().uuid(),
  is_completed: z.boolean(),
  start_date: z.string().date(),
  task_id: z.string().uuid(),
  updated_at: z.string().datetime(),
});

export const zTaskRecordsList = z.object({
  items: z.array(zTaskRecord).optional(),
  pagination: zPagination.optional(),
});

export const zTaskRequest = z.object({
  description: z.union([z.string().max(300), z.null()]).optional(),
  enable_notifications: z.boolean().optional().default(true),
  is_active: z.boolean().optional().default(true),
  name: z.string().min(3).max(50),
  period_type: z.enum(["week", "month", "quarter", "year"]).optional(),
  target_amount: z.union([z.string().regex(/^(-|\+)?((\d+(\.\d*)?)|(\.\d+))$/), z.null()]).optional(),
});

export const zTasksList = z.object({
  items: z.array(zTask).optional(),
  pagination: zPagination.optional(),
});

export const zTokenRequest = z.object({
  email: z.string().email(),
  password: z.string(),
});

export const zTokenResponse = z.object({
  access_token: z.string(),
});

export const zTransaction = z.object({
  account: zAccount,
  account_id: z.string().uuid(),
  account_to: z.union([z.union([z.object({}), z.null()]), zAccount]),
  account_to_id: z.union([z.string().uuid(), z.null()]),
  amount: z.string().regex(/^(-|\+)?((\d+(\.\d*)?)|(\.\d+))$/),
  amount_to: z.string().regex(/^(-|\+)?((\d+(\.\d*)?)|(\.\d+))$/),
  base_amount: z.string().regex(/^(-|\+)?((\d+(\.\d*)?)|(\.\d+))$/),
  base_amount_to: z.string().regex(/^(-|\+)?((\d+(\.\d*)?)|(\.\d+))$/),
  category: z.union([z.union([z.object({}), z.null()]), zCategory]),
  category_id: z.union([z.string().uuid(), z.null()]),
  created_at: z.string().datetime(),
  description: z.union([z.string(), z.null()]),
  id: z.string().uuid(),
  task_id: z.union([z.string().uuid(), z.null()]).optional(),
  transaction_date: z.string().date(),
  transaction_type: z.enum(["income", "expense", "transfer"]),
  updated_at: z.string().datetime(),
});

export const zTransactionList = z.object({
  items: z.array(zTransaction).optional(),
  pagination: zPagination.optional(),
});

export const zTransactionRequest = z.object({
  account_id: z.string().uuid(),
  account_to_id: z.union([z.string().uuid(), z.null()]).optional(),
  amount: z.string().regex(/^(-|\+)?((\d+(\.\d*)?)|(\.\d+))$/),
  amount_to: z
    .string()
    .regex(/^(-|\+)?((\d+(\.\d*)?)|(\.\d+))$/)
    .optional()
    .default("0.00"),
  category_id: z.union([z.string().uuid(), z.null()]).optional(),
  description: z.union([z.string(), z.null()]).optional(),
  task_id: z.union([z.string().uuid(), z.null()]).optional(),
  transaction_date: z.string().date(),
  transaction_type: z.enum(["income", "expense", "transfer"]).optional(),
});

export const zUser = z.object({
  base_currency: z.enum(["EUR", "UAH", "USD"]),
  email: z.string().email(),
  id: z.string().uuid(),
  name: z.string(),
});

export const zValidationError = z.object({
  detail: z
    .object({
      "<location>": z
        .object({
          "<field_name>": z.array(z.string()).optional(),
        })
        .optional(),
    })
    .optional(),
  message: z.string().optional(),
});

export const zGetAccountsResponse = z.array(zAccount);

export const zCreateAccountResponse = zAccount;

export const zRemoveAccountResponse = z.void();

export const zGetAccountResponse = zAccount;

export const zUpdateAccountResponse = zAccount;

export const zSetAccountBalanceResponse = zAccount;

export const zLogoutResponse = z.void();

export const zRequestPasswordResetResponse = zPasswordResetResponse;

export const zResetPasswordResponse = zPasswordResetSuccess;

export const zGetTokenResponse = zTokenResponse;

export const zGetBudgetsResponse = z.array(zBudget);

export const zCreateBudgetResponse = zBudget;

export const zDeleteBudgetResponse = z.void();

export const zGetBudgetResponse = zBudget;

export const zUpdateBudgetResponse = zBudget;

export const zGetBudgetPeriodsResponse = zBudgetPeriodsList;

export const zUpdateBudgetAmountResponse = zBudget;

export const zGetCategoriesResponse = z.array(zCategory);

export const zCreateCategoryResponse = zCategory;

export const zRemoveCategoryResponse = z.void();

export const zGetCategoryResponse = zCategory;

export const zUpdateCategoryResponse = zCategory;

export const zGetFinancialSummaryResponse = zSummary;

export const zGetSavingsGoalsResponse = z.array(zSavingsGoal);

export const zCreateSavingsGoalResponse = zSavingsGoal;

export const zRemoveSavingsGoalResponse = z.void();

export const zGetSavingsGoalResponse = zSavingsGoal;

export const zUpdateSavingsGoalResponse = zSavingsGoal;

export const zUpdateSavingsGoalAccountsResponse = zSavingsGoal;

export const zUpdateSavingsGoalStatusResponse = zSavingsGoal;

export const zGetTasksResponse = zTasksList;

export const zCreateTaskResponse = zTask;

export const zGetOverdueTasksResponse = z.array(zTask);

export const zGetUpcomingTasksResponse = z.array(zTask);

export const zDeleteTaskResponse = z.void();

export const zGetTaskResponse = zTask;

export const zUpdateTaskResponse = zTask;

export const zCompleteTaskResponse = zTask;

export const zGetTaskRecordsResponse = zTaskRecordsList;

export const zGetTransactionsResponse = zTransactionList;

export const zCreateTransactionResponse = zTransaction;

export const zRemoveTransactionResponse = z.void();

export const zGetTransactionResponse = zTransaction;

export const zUpdateTransactionResponse = zTransaction;

export const zCreateUserResponse = zUser;

export const zCurrentUserResponse = zUser;
