import { z } from "zod";

import { zSavingsGoalCreateRequest, zSavingsGoalUpdateRequest } from "~/api/zod.gen";

export const CreateSavingsGoalSchema = zSavingsGoalCreateRequest.extend({
  target_amount: z.preprocess(
    (val) => (val !== undefined ? (val === "" ? null : val) : null),
    zSavingsGoalCreateRequest.shape.target_amount
  ),
  target_date: z.preprocess(
    (val) => (val !== undefined ? (val === "" ? null : val) : null),
    zSavingsGoalCreateRequest.shape.target_date
  ),
});

export const UpdateSavingsGoalSchema = zSavingsGoalUpdateRequest.extend({
  target_amount: z.preprocess(
    (val) => (val !== undefined ? (val === "" ? null : val) : null),
    zSavingsGoalCreateRequest.shape.target_amount
  ),
  target_date: z.preprocess(
    (val) => (val !== undefined ? (val === "" ? null : val) : null),
    zSavingsGoalCreateRequest.shape.target_date
  ),
});
