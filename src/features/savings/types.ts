import type { SavingsGoal as ApiSavingsGoal } from "~/api";

import { z } from "zod";

import { CreateSavingsGoalSchema, UpdateSavingsGoalSchema } from "./schemas";

export type SavingsGoal = ApiSavingsGoal;

export type SavingsGoalStatus = "active" | "completed" | "archived";

export interface SavingsGoalData {
  name: string;
  currency: "EUR" | "UAH" | "USD";
  description: string | null;
  target_amount: string | null;
  target_date: string | null;
  account_ids: string[];
}

export interface SavingsGoalFilters {
  status: SavingsGoalStatus;
}

export type SavingsGoalCreateData = z.infer<typeof CreateSavingsGoalSchema>;
export type SavingsGoalUpdateData = z.infer<typeof UpdateSavingsGoalSchema>;
