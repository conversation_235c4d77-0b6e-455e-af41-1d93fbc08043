import { useMemo } from "react";

import { useQuery } from "@tanstack/react-query";

import { getSavingsGoalsOptions } from "~/api/@tanstack/react-query.gen";
import type { SavingsGoal, SavingsGoalStatus } from "../types";

export function useSavingsGoals() {
  const { data, isLoading, isError, error } = useQuery({
    ...getSavingsGoalsOptions(),
  });

  const goals = useMemo(() => data ?? [], [data]);
  
  const activeGoals = useMemo(() => 
    goals.filter((goal) => goal.status === "active"), 
    [goals]
  );
  
  const completedGoals = useMemo(() => 
    goals.filter((goal) => goal.status === "completed"), 
    [goals]
  );
  
  const archivedGoals = useMemo(() => 
    goals.filter((goal) => goal.status === "archived"), 
    [goals]
  );

  const getFilteredGoals = (status: SavingsGoalStatus) => {
    switch (status) {
      case "active":
        return activeGoals;
      case "completed":
        return completedGoals;
      case "archived":
        return archivedGoals;
      default:
        return activeGoals;
    }
  };

  const getTotalAmount = (goals: SavingsGoal[]) => {
    return goals.reduce((total, goal) => {
      return total + parseFloat(goal.current_amount);
    }, 0);
  };

  return { 
    isLoading, 
    isError, 
    error, 
    goals, 
    activeGoals, 
    completedGoals, 
    archivedGoals,
    getFilteredGoals,
    getTotalAmount
  };
}
