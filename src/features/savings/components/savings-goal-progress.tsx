import { cn } from "~/lib/utils";

interface SavingsGoalProgressProps extends React.HTMLAttributes<HTMLDivElement> {
  value: number;
  max?: number;
}

export default function SavingsGoalProgress({ className, value, max = 100, ...props }: SavingsGoalProgressProps) {
  const percentage = max > 0 ? Math.min(Math.max(0, (value / max) * 100), 100) : 0;

  return (
    <div className={cn("relative h-4 w-full overflow-hidden rounded-full bg-gray-200", className)} {...props}>
      <div
        className={cn("h-full w-full flex-1 transition-all", {
          "bg-emerald-500": percentage < 75,
          "bg-orange-500": percentage >= 75 && percentage < 99,
          "bg-rose-500": percentage >= 99,
        })}
        style={{ transform: `translateX(-${(100 - percentage).toFixed(2)}%)` }}
      />
    </div>
  );
}
