import { useMemo } from "react";

import { Link } from "@tanstack/react-router";
import { ChevronRightIcon, PiggyBankIcon } from "lucide-react";

import { ContentBlock } from "~/components/blocks";
import BlockLoader from "~/components/elements/block-loader";
import { useCurrentUser } from "~/features/auth/hooks";
import { formatCurrency } from "~/features/currencies/formatters";

import { useSavingsGoals } from "../hooks";

export default function SavingsGoalsWidget() {
  const currentUser = useCurrentUser();
  const { activeGoals, isLoading, getTotalAmount } = useSavingsGoals();

  const totalAmount = useMemo(() => {
    return getTotalAmount(activeGoals);
  }, [getTotalAmount, activeGoals]);

  // Show at most 3 goals on the dashboard
  const displayGoals = useMemo(
    () => activeGoals.sort((a, b) => (a.updated_at <= b.updated_at ? 1 : -1)).slice(0, 3),
    [activeGoals]
  );

  if (isLoading) {
    return (
      <ContentBlock title="Savings">
        <BlockLoader className="min-h-20" />
      </ContentBlock>
    );
  }

  if (activeGoals.length === 0) {
    return (
      <ContentBlock
        title="Savings"
        action={
          <Link
            to="/savings"
            className="text-gray-02 flex flex-row items-center gap-2 text-xs leading-4 font-medium hover:underline"
          >
            <span>View all</span> <ChevronRightIcon className="size-4" />
          </Link>
        }
      >
        <div className="flex h-full min-h-24 items-center justify-center rounded-lg border border-dashed border-gray-300">
          <p className="text-sm text-gray-500">No active savings goals</p>
        </div>
      </ContentBlock>
    );
  }

  return (
    <ContentBlock
      title="Savings"
      action={
        <Link to="/savings" className="link text-xs">
          <span>View all</span> <ChevronRightIcon className="size-4" />
        </Link>
      }
    >
      <div className="flex flex-col gap-4">
        <div className="flex items-center justify-between border-b border-gray-100 pb-3">
          <p className="text-xl font-bold">{formatCurrency(currentUser.base_currency, totalAmount.toString())}</p>
          <p className="text-sm text-gray-500">Total savings</p>
        </div>

        {displayGoals.map((goal) => {
          const currentAmount = parseFloat(goal.current_amount);
          const targetAmount = goal.target_amount ? parseFloat(goal.target_amount) : 0;
          const hasTarget = targetAmount > 0;
          const progress = hasTarget ? Math.min(Math.round((currentAmount / targetAmount) * 100), 100) : null;

          return (
            <div key={goal.id} className="flex items-center justify-between">
              <div className="flex items-center gap-2">
                <div className="self-stretch">
                  <PiggyBankIcon className="text-dark-secondary size-4" />
                </div>
                <div>
                  <p className="text-gray-02 text-xs">{goal.name}</p>
                  <p className="text-dark text-base font-bold">{formatCurrency(goal.currency, goal.current_amount)}</p>
                </div>
              </div>
              {progress !== null && (
                <div className="rounded-full bg-gray-100 px-2 py-1 text-xs font-medium">{progress}%</div>
              )}
            </div>
          );
        })}
      </div>
    </ContentBlock>
  );
}
