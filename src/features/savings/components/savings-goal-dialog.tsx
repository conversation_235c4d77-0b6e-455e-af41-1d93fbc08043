import type { SavingsGoal } from "../types";

import { useToggle } from "react-use";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import { getSavingsGoalsQueryKey, removeSavingsGoalMutation } from "~/api/@tanstack/react-query.gen";
import { Button } from "~/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "~/components/ui/dialog";
import { useConfirmation } from "~/features/confirmations";

import SavingsGoalCreateForm from "./savings-goal-create-form";
import SavingsGoalUpdateForm from "./savings-goal-update-form";

interface Props {
  goal?: SavingsGoal;
  children: React.ReactNode;
}

export default function SavingsGoalDialog({ goal, children: trigger }: Props) {
  const queryClient = useQueryClient();
  const ask = useConfirmation();

  const [open, toggleOpen] = useToggle(false);

  const isCreate = goal === undefined;
  const isUpdate = goal !== undefined;

  const { mutate: removeGoal } = useMutation({
    ...removeSavingsGoalMutation(),
    onSuccess: () => {
      void queryClient.invalidateQueries({ queryKey: getSavingsGoalsQueryKey() });
      toast("Savings goal removed successfully!");
      toggleOpen(false);
    },
  });

  const handleRemove = async () => {
    const confirmed = await ask({
      title: "Remove Savings Goal",
      description: `Are you sure you want to remove "${goal!.name}"? This action cannot be undone.`,
    });

    if (confirmed) {
      removeGoal({ path: { goal_id: goal!.id } });
    }
  };

  return (
    <Dialog open={open} onOpenChange={toggleOpen}>
      <DialogTrigger asChild>{trigger}</DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{isCreate ? "Create savings goal" : `Edit ${goal.name}`}</DialogTitle>
          <DialogDescription>
            {isCreate ? "Create a new savings goal to track your progress." : "Update savings goal details"}
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          {isCreate && <SavingsGoalCreateForm formId="savings-goal-form" onSuccess={toggleOpen} />}
          {isUpdate && <SavingsGoalUpdateForm formId="savings-goal-form" goal={goal} onSuccess={toggleOpen} />}
        </div>

        <DialogFooter>
          {isUpdate && (
            <Button type="button" variant="secondary" className="me-auto" onClick={handleRemove}>
              Remove
            </Button>
          )}
          <DialogClose asChild>
            <Button variant="ghost">Cancel</Button>
          </DialogClose>
          <Button type="submit" form="savings-goal-form">
            {isCreate ? "Create" : "Update"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
