import type { SavingsGoalCreateData } from "../types";

import { useMemo } from "react";
import { useForm } from "react-hook-form";

import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import { createSavingsGoalMutation, getSavingsGoalsQueryKey } from "~/api/@tanstack/react-query.gen";
import InputCurrency from "~/components/inputs/input-currency";
import InputMultiSelect from "~/components/inputs/input-multi-select";
import InputText from "~/components/inputs/input-text";
import { Form } from "~/components/ui/form";
import { useAccounts } from "~/features/accounts/hooks";
import { useCurrentUser } from "~/features/auth/hooks";

import { CreateSavingsGoalSchema } from "../schemas";

interface Props {
  formId: string;
  onSuccess?: () => void;
}

export default function SavingsGoalCreateForm({ formId, onSuccess }: Props) {
  const queryClient = useQueryClient();
  const currentUser = useCurrentUser();
  const { activeAccounts } = useAccounts();

  const accountOptions = useMemo(() => {
    return activeAccounts.map((account) => ({
      value: account.id,
      label: account.name,
    }));
  }, [activeAccounts]);

  const { mutate: createGoal } = useMutation({
    ...createSavingsGoalMutation(),
    onSuccess: () => {
      void queryClient.invalidateQueries({ queryKey: getSavingsGoalsQueryKey() });
      toast("Savings goal created successfully!");
      onSuccess?.();
    },
  });

  const form = useForm<SavingsGoalCreateData>({
    resolver: zodResolver(CreateSavingsGoalSchema),
    defaultValues: {
      name: "",
      currency: currentUser.base_currency,
      description: "",
      target_amount: "",
      target_date: "",
      account_ids: [],
    },
  });

  const onSubmit = form.handleSubmit((data) => {
    const body = {
      ...data,
      description: data.description || null,
      target_amount: data.target_amount || null,
      target_date: data.target_date || null,
    };

    createGoal({ body });
  });

  return (
    <Form {...form}>
      <form id={formId} onSubmit={onSubmit} className="space-y-4">
        <InputText control={form.control} name="name" label="Goal Name" placeholder="e.g., New Car, Vacation" />

        <InputCurrency control={form.control} name="currency" label="Currency" />

        <InputText
          control={form.control}
          name="target_amount"
          label="Target Amount"
          placeholder="0.00"
          type="number"
          step="0.01"
        />

        <InputText control={form.control} name="target_date" label="Target Date" type="date" />

        <InputMultiSelect
          control={form.control}
          name="account_ids"
          label="Linked Accounts"
          placeholder="Select accounts"
          options={accountOptions}
        />

        <InputText control={form.control} name="description" label="Description" placeholder="Optional description" />
      </form>
    </Form>
  );
}
