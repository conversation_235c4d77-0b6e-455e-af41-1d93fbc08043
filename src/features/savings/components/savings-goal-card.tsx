import type { SavingsGoal } from "../types";

import { PencilLineIcon, PiggyBankIcon } from "lucide-react";

import { Gauge<PERSON>hart } from "~/components/charts";
import { WidgetValue } from "~/components/elements";
import { TargetArrowIcon } from "~/components/icons";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardFooter, CardHeader } from "~/components/ui/card";
import { formatAmountShort } from "~/lib/formatters";

import SavingsGoalDialog from "./savings-goal-dialog";

interface Props {
  goal: SavingsGoal;
}

export default function SavingsGoalCard({ goal }: Props) {
  const currentAmount = parseFloat(goal.current_amount);
  const targetAmount = goal.target_amount ? parseFloat(goal.target_amount) : 0;
  const hasTargetAmount = !!goal.target_amount && Number(goal.target_amount) > 0;

  const isActive = goal.status === "active";
  const isCompleted = goal.status === "completed";
  const isArchived = goal.status === "archived";

  return (
    <Card className="py-4">
      <CardHeader className="mx-4 border-b border-gray-200 px-0 pb-2 md:flex-row md:items-center md:justify-between">
        <h4 className="text-dark block text-xl font-bold">{goal.name}</h4>
        <div className="flex items-center gap-1 rounded-full bg-gray-100 px-2 py-1 text-xs font-medium">
          {isActive && <span className="text-emerald-600">Active</span>}
          {isCompleted && <span className="text-blue-600">Completed</span>}
          {isArchived && <span className="text-gray-600">Archived</span>}
        </div>
      </CardHeader>

      <CardContent className="flex-grow">
        {goal.description && <p className="text-xs text-gray-900">{goal.description}</p>}
        <div className="grid grid-cols-2 items-center gap-2">
          <div className="flex flex-col gap-5 pt-3">
            <WidgetValue
              label="Saved"
              currency={goal.currency}
              value={goal.current_amount}
              icon={<PiggyBankIcon className="text-dark-secondary size-4" />}
            />
            {hasTargetAmount && (
              <WidgetValue
                label="Target"
                currency={goal.currency}
                value={goal.target_amount!}
                icon={<TargetArrowIcon className="text-dark-secondary size-4" />}
              />
            )}
          </div>

          {hasTargetAmount && (
            <GaugeChart
              value={currentAmount}
              min={0}
              max={targetAmount}
              label="Progress"
              formatValue={(value) => formatAmountShort(value, goal.currency)}
            />
          )}

          {!hasTargetAmount && (
            <div>
              {/* Replace with goal icon */}
              <PiggyBankIcon className="text-primary mx-auto size-32 stroke-[0.8]" />
            </div>
          )}
        </div>
      </CardContent>

      <CardFooter className="px-4 pt-0">
        <SavingsGoalDialog goal={goal}>
          <Button variant="outline" className="mx-auto">
            Adjust goal <PencilLineIcon className="size-4" />
          </Button>
        </SavingsGoalDialog>
      </CardFooter>
    </Card>
  );
}
