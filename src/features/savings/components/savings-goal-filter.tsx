import { useCallback, useState } from "react";

import { Button } from "~/components/ui/button";
import type { SavingsGoalStatus } from "../types";

interface Props {
  activeCount: number;
  completedCount: number;
  archivedCount: number;
  onChange: (status: SavingsGoalStatus) => void;
}

export default function SavingsGoalFilter({ activeCount, completedCount, archivedCount, onChange }: Props) {
  const [selectedStatus, setSelectedStatus] = useState<SavingsGoalStatus>("active");

  const handleChange = useCallback(
    (status: SavingsGoalStatus) => {
      setSelectedStatus(status);
      onChange(status);
    },
    [onChange]
  );

  return (
    <div className="flex items-center gap-2">
      <Button
        variant={selectedStatus === "active" ? "default" : "outline"}
        size="sm"
        onClick={() => handleChange("active")}
      >
        Active ({activeCount})
      </Button>
      
      {completedCount > 0 && (
        <Button
          variant={selectedStatus === "completed" ? "default" : "outline"}
          size="sm"
          onClick={() => handleChange("completed")}
        >
          Completed ({completedCount})
        </Button>
      )}
      
      {archivedCount > 0 && (
        <Button
          variant={selectedStatus === "archived" ? "default" : "outline"}
          size="sm"
          onClick={() => handleChange("archived")}
        >
          Archived ({archivedCount})
        </Button>
      )}
    </div>
  );
}
