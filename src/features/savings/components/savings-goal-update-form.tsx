import type { SavingsGoal, SavingsGoalUpdateData } from "../types";

import { useMemo } from "react";
import { useForm } from "react-hook-form";

import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import {
  getSavingsGoalQueryKey,
  getSavingsGoalsQueryKey,
  updateSavingsGoalMutation,
} from "~/api/@tanstack/react-query.gen";
import InputMultiSelect from "~/components/inputs/input-multi-select";
import InputText from "~/components/inputs/input-text";
import { Form } from "~/components/ui/form";
import { useAccounts } from "~/features/accounts/hooks";

import { UpdateSavingsGoalSchema } from "../schemas";

interface Props {
  formId: string;
  goal: SavingsGoal;
  onSuccess?: () => void;
}

export default function SavingsGoalUpdateForm({ formId, goal, onSuccess }: Props) {
  const queryClient = useQueryClient();
  const { activeAccounts } = useAccounts();

  const accountOptions = useMemo(() => {
    return activeAccounts.map((account) => ({
      value: account.id,
      label: account.name,
    }));
  }, [activeAccounts]);

  const { mutate: updateGoal } = useMutation({
    ...updateSavingsGoalMutation(),
    onSuccess: () => {
      void queryClient.invalidateQueries({ queryKey: getSavingsGoalsQueryKey() });
      void queryClient.invalidateQueries({ queryKey: getSavingsGoalQueryKey({ path: { goal_id: goal.id } }) });
      toast("Savings goal updated successfully!");
      onSuccess?.();
    },
  });

  const form = useForm<SavingsGoalUpdateData>({
    resolver: zodResolver(UpdateSavingsGoalSchema),
    defaultValues: {
      name: goal.name,
      description: goal.description || "",
      target_amount: goal.target_amount || "",
      target_date: goal.target_date || "",
      account_ids: goal.accounts.map((account) => account.id),
    },
  });

  const onSubmit = form.handleSubmit((data) => {
    const body = {
      ...data,
      description: data.description || null,
      target_amount: data.target_amount || null,
      target_date: data.target_date || null,
    };

    updateGoal({ body, path: { goal_id: goal.id } });
  });

  return (
    <Form {...form}>
      <form id={formId} onSubmit={onSubmit} className="space-y-4">
        <InputText control={form.control} name="name" label="Goal Name" placeholder="e.g., New Car, Vacation" />

        <InputText
          control={form.control}
          name="target_amount"
          label="Target Amount"
          placeholder="0.00"
          type="number"
          step="0.01"
        />

        <InputText control={form.control} name="target_date" label="Target Date" type="date" />

        <InputMultiSelect
          control={form.control}
          name="account_ids"
          label="Linked Accounts"
          placeholder="Select accounts"
          options={accountOptions}
        />

        <InputText control={form.control} name="description" label="Description" placeholder="Optional description" />
      </form>
    </Form>
  );
}
