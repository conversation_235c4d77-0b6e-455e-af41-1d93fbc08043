import type { Actions, State } from "./types";

import { create } from "zustand";
import { devtools } from "zustand/middleware";
import { immer } from "zustand/middleware/immer";

export const useAuthStore = create<State & Actions>()(
  devtools(
    immer((set) => ({
      user: null,
      isAuthenticated: false,
      isInitialized: false,

      setState(newState) {
        set(
          (state) => {
            Object.assign(state, newState);
          },
          undefined,
          "auth/setState"
        );
      },
      setUser(user) {
        set(
          (state) => {
            state.user = user;
          },
          undefined,
          "auth/setUser"
        );
      },
      setAuthenticated(isAuthenticated) {
        set(
          (state) => {
            state.isAuthenticated = isAuthenticated;
          },
          undefined,
          "auth/setAuthenticated"
        );
      },
      setInitialized(isInitialized) {
        set(
          (state) => {
            state.isInitialized = isInitialized;
          },
          undefined,
          "auth/setInitialized"
        );
      },
    })),
    { name: "auth" }
  )
);
