import type { CreateUserRequest, PasswordReset, PasswordResetRequest, TokenRequest } from "~/api";

import { CancelledError, useQuery } from "@tanstack/react-query";
import { toast } from "sonner";
import { useShallow } from "zustand/react/shallow";

import { createUser, getToken, requestPasswordReset, resetPassword } from "~/api";
import { currentUserOptions } from "~/api/@tanstack/react-query.gen";
import queryClient from "~/queryClient";
import router from "~/router";

import { useAuthStore } from "./store";

export function useCurrentUser() {
  return useAuthStore((state) => state.user!);
}

export function useAuthenticated() {
  return useAuthStore((state) => state.isAuthenticated);
}

export function useAuth() {
  const { user, isAuthenticated, isInitialized, setState } = useAuthStore(useShallow((state) => state));
  const { refetch: fetchCurrentUser } = useQuery({ ...currentUserOptions(), enabled: false, retry: false });

  const initialize = async (abortSignal: AbortSignal) => {
    try {
      const result = await queryClient.ensureQueryData({ ...currentUserOptions({ signal: abortSignal }) });
      setState({ user: result, isAuthenticated: true, isInitialized: true });
    } catch (err) {
      if (err instanceof CancelledError) {
        return;
      }

      setState({ user: null, isAuthenticated: false, isInitialized: true });
    }
  };

  const register = async (data: CreateUserRequest) => {
    const userResult = await createUser({ body: data });

    if (userResult.error !== undefined) {
      return (userResult.error as { message?: string }).message ?? userResult.message;
    }

    const tokenResult = await getToken({ body: { email: data.email, password: data.password } });
    if (tokenResult.error !== undefined) {
      return (tokenResult.error as { message?: string }).message ?? tokenResult.message;
    }

    setState({ user: userResult.data, isAuthenticated: true });

    await router.invalidate();

    toast("Welcome to Finanze.Pro!", { description: "Thank you for signing up!" });

    return true;
  };

  const login = async (data: TokenRequest) => {
    const result = await getToken({ body: data });

    if (result.error !== undefined) {
      return (result.error as { message?: string }).message ?? result.message;
    }

    const userResult = await fetchCurrentUser();
    if (userResult.error) {
      return userResult.error.message;
    }

    if (userResult.data === undefined) {
      return "Unknown error. Please try again later.";
    }

    setState({ user: userResult.data, isAuthenticated: true });

    await router.invalidate();

    toast("Welcome back!", { description: "Have a great day!" });

    return true;
  };

  const logout = async () => {
    setState({ user: null, isAuthenticated: false });

    await router.invalidate();

    toast("See you soon!", { description: "You have been logged out." });
  };

  const requestPasswordResetEmail = async (data: PasswordResetRequest) => {
    const result = await requestPasswordReset({ body: data });

    if (result.error !== undefined) {
      return (result.error as { message?: string }).message ?? result.message;
    }

    toast("Password reset email sent", { description: "Please check your email for further instructions." });

    return true;
  };

  const resetUserPassword = async (data: PasswordReset) => {
    const result = await resetPassword({ body: data });

    if (result.error !== undefined) {
      return (result.error as { message?: string }).message ?? result.message;
    }

    toast("Password reset successful", { description: "You can now login with your new password." });

    return true;
  };

  return {
    user,
    isAuthenticated,
    isInitialized,
    initialize,
    register,
    login,
    logout,
    requestPasswordResetEmail,
    resetUserPassword,
  };
}
