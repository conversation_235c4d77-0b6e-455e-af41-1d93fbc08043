/**
 * Types for the confirmation system
 */

/**
 * Confirmation options
 */
export interface ConfirmationOptions {
  /**
   * The title of the confirmation dialog
   */
  title: string;

  /**
   * The description of the confirmation dialog
   */
  description: string;

  /**
   * The text for the confirm button
   * @default "Confirm"
   */
  confirmText?: string;

  /**
   * The text for the cancel button
   * @default "Cancel"
   */
  cancelText?: string;

  /**
   * The variant of the confirm button
   * @default "default"
   */
  confirmVariant?: "default" | "destructive" | "outline" | "secondary" | "ghost" | "link";
}

/**
 * Confirmation state
 */
export interface ConfirmationState {
  /**
   * Whether the confirmation dialog is open
   */
  isOpen: boolean;

  /**
   * The options for the confirmation dialog
   */
  options: ConfirmationOptions;

  /**
   * The resolve function for the promise
   */
  resolve?: (value: boolean) => void;

  /**
   * Open the confirmation dialog
   */
  open: (options: ConfirmationOptions) => Promise<boolean>;

  /**
   * Close the confirmation dialog
   */
  close: (result: boolean) => void;
}
