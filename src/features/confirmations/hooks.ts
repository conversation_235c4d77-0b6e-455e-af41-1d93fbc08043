/**
 * Hooks for the confirmation system
 */

import type { ConfirmationOptions } from "./types";

import { useConfirmationStore } from "./store";

/**
 * Hook for using the confirmation system
 * @returns A function to open the confirmation dialog
 * @example
 * ```tsx
 * const confirm = useConfirmation();
 *
 * const handleDelete = async () => {
 *   const ok = await confirm({
 *     title: "Delete Account",
 *     description: "Are you sure you want to delete this account?",
 *     confirmText: "Delete",
 *     confirmVariant: "destructive",
 *   });
 *
 *   if (ok) {
 *     // Delete the account
 *     deleteAccount(id);
 *   }
 * };
 * ```
 */
export function useConfirmation() {
  const { open } = useConfirmationStore();

  /**
   * Open the confirmation dialog
   * @param options The options for the confirmation dialog
   * @returns A promise that resolves to true if the user confirms, false otherwise
   */
  const confirm = (options: ConfirmationOptions): Promise<boolean> => {
    return open(options);
  };

  return confirm;
}
