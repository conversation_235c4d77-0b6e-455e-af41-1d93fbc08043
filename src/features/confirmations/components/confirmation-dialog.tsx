/**
 * Confirmation dialog component
 */

import {
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
} from "~/components/ui/alert-dialog";

import { useConfirmationStore } from "../store";

/**
 * Confirmation dialog component
 * This component should be rendered once at the root of the application
 */
export default function ConfirmationDialog() {
  const { isOpen, options, close } = useConfirmationStore();

  /**
   * Handle confirm action
   */
  const handleConfirm = () => {
    close(true);
  };

  /**
   * Handle cancel action
   */
  const handleCancel = () => {
    close(false);
  };

  return (
    <AlertDialog open={isOpen} onOpenChange={(open) => !open && close(false)}>
      <AlertDialogContent>
        <AlertDialogHeader>
          <AlertDialogTitle>{options.title}</AlertDialogTitle>
          <AlertDialogDescription>{options.description}</AlertDialogDescription>
        </AlertDialogHeader>
        <AlertDialogFooter>
          <AlertDialogCancel onClick={handleCancel}>{options.cancelText || "Cancel"}</AlertDialogCancel>
          <AlertDialogAction onClick={handleConfirm} variant={options.confirmVariant}>
            {options.confirmText || "Confirm"}
          </AlertDialogAction>
        </AlertDialogFooter>
      </AlertDialogContent>
    </AlertDialog>
  );
}
