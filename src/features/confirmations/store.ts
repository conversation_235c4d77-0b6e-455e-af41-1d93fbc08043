/**
 * Zustand store for managing confirmation state
 */

import type { ConfirmationOptions, ConfirmationState } from "./types";

import { create } from "zustand";
import { immer } from "zustand/middleware/immer";

/**
 * Default confirmation options
 */
const DEFAULT_OPTIONS: ConfirmationOptions = {
  title: "Confirmation",
  description: "Are you sure you want to continue?",
  confirmText: "Confirm",
  cancelText: "Cancel",
  confirmVariant: "default",
};

/**
 * Confirmation store
 */
export const useConfirmationStore = create<ConfirmationState>()(
  immer((set, get) => ({
    isOpen: false,
    options: DEFAULT_OPTIONS,
    resolve: undefined,

    /**
     * Open the confirmation dialog
     */
    open: (options) => {
      return new Promise<boolean>((resolve) => {
        set((state) => {
          state.isOpen = true;
          state.options = {
            ...DEFAULT_OPTIONS,
            ...options,
          };
          state.resolve = resolve;
        });
      });
    },

    /**
     * Close the confirmation dialog
     */
    close: (result: boolean) => {
      const { resolve } = get();

      if (resolve) {
        resolve(result);
      }

      set((state) => {
        state.isOpen = false;
        state.resolve = undefined;
      });
    },
  }))
);
