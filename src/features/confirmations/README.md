# Confirmation System

A nice confirmation system for the application instead of browser alerts.

## Features

- Uses Zustand for state management
- Uses shadcn UI components for the dialog
- Provides a simple hook API for triggering confirmations
- Supports custom titles, descriptions, and button text
- Supports different button variants (default, destructive, etc.)

## Usage

### 1. Add the ConfirmationDialog to your app

The `ConfirmationDialog` component should be rendered once at the root of your application:

```tsx
// app.tsx
import { ConfirmationDialog } from "~/features/confirmations";

function App() {
  return (
    <>
      {/* Your app content */}
      <ConfirmationDialog />
    </>
  );
}
```

### 2. Use the useConfirmation hook in your components

```tsx
import { useConfirmation } from "~/features/confirmations";

function MyComponent() {
  const confirm = useConfirmation();

  const handleDelete = async () => {
    const ok = await confirm({
      title: "Delete Item",
      description: "Are you sure you want to delete this item? This action cannot be undone.",
      confirmText: "Delete",
      confirmVariant: "destructive",
    });

    if (ok) {
      // User confirmed
      deleteItem();
    } else {
      // User cancelled (optional handling)
      console.log("Deletion cancelled");
    }
  };

  return (
    <button onClick={handleDelete}>
      Delete
    </button>
  );
}
```

## API

### useConfirmation

```tsx
const confirm = useConfirmation();

// Returns a Promise<boolean>
const result = await confirm(options);
```

#### Options

| Property | Type | Description | Default |
|----------|------|-------------|---------|
| title | string | The title of the confirmation dialog | "Confirmation" |
| description | string | The description of the confirmation dialog | "Are you sure you want to continue?" |
| confirmText | string | The text for the confirm button | "Confirm" |
| cancelText | string | The text for the cancel button | "Cancel" |
| confirmVariant | string | The variant of the confirm button | "default" |

#### Return Value

The `confirm` function returns a Promise that resolves to:
- `true` if the user confirms the action
- `false` if the user cancels the action

## Examples

### Basic Confirmation

```tsx
const confirm = useConfirmation();

const handleAction = async () => {
  const ok = await confirm({
    title: "Confirmation",
    description: "Are you sure you want to continue?",
  });

  if (ok) {
    console.log("User confirmed");
  }
};
```

### Delete Confirmation

```tsx
const confirm = useConfirmation();

const handleDelete = async (id) => {
  const ok = await confirm({
    title: "Delete Account",
    description: "Are you sure you want to delete this account? This action cannot be undone.",
    confirmText: "Delete",
    confirmVariant: "destructive",
  });

  if (ok) {
    deleteAccount(id);
  }
};
```

### Custom Button Text

```tsx
const confirm = useConfirmation();

const handleLogout = async () => {
  const ok = await confirm({
    title: "Logout",
    description: "Are you sure you want to logout?",
    confirmText: "Yes, logout",
    cancelText: "No, stay logged in",
  });

  if (ok) {
    logout();
  }
};
```
