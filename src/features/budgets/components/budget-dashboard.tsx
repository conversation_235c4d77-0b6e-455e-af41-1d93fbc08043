import { useMemo } from "react";

import { GoalIcon, HandCoinsIcon } from "lucide-react";

import { BudgetPeriod } from "~/api";
import { ContentBlock } from "~/components/blocks";
import { GaugeChart } from "~/components/charts";
import { BlockLoader, WidgetValue } from "~/components/elements";
import { useCurrentUser } from "~/features/auth/hooks";
import { formatCurrency } from "~/features/currencies/formatters";
import { formatAmountShort } from "~/lib/formatters";

import { useBudgets } from "../hooks";

export default function BudgetDashboard() {
  const currentUser = useCurrentUser();

  const { isLoading, favoriteBudgets } = useBudgets();

  // First active budget with current period for now
  // todo: add separate components and simple pagination for multiple budgets
  const budget = useMemo(
    () =>
      favoriteBudgets.length > 0
        ? favoriteBudgets.filter((budget) => budget.is_active && !!budget.current_period).at(0)
        : null,
    [favoriteBudgets]
  );

  if (isLoading) {
    return <BlockLoader className="min-h-20" />;
  }

  if (!budget) {
    return (
      <ContentBlock title="Budgets">
        <p className="px-4 py-4 text-sm text-gray-600 italic">No favorite budgets found</p>
      </ContentBlock>
    );
  }

  if (!budget.current_period) {
    return (
      <ContentBlock title="Budgets">
        <p className="px-4 py-4 text-sm text-gray-600 italic">No active budget periods found</p>
      </ContentBlock>
    );
  }

  return (
    <ContentBlock title="Budgets">
      <div>
        <div className="flex flex-row items-center justify-between gap-2 border-b border-gray-100 pb-3">
          <p className="text-dark block text-xl font-bold">
            {formatCurrency(currentUser.base_currency, (budget.current_period as BudgetPeriod).planned_amount)}
          </p>
          <p className="text-dark-secondary block pt-0.5 text-sm font-medium">
            {budget.period_type === "month" && "This month"}
            {budget.period_type === "week" && "This week"}
            {budget.period_type === "year" && "This year"}
          </p>
        </div>

        <div className="grid grid-cols-2 gap-2 pt-4">
          <div className="flex flex-col gap-5 pt-3">
            <WidgetValue
              label="Planned amount"
              currency={currentUser.base_currency}
              value={budget.current_period.planned_amount as string}
              icon={<GoalIcon className="text-dark-secondary size-4" />}
            />
            <WidgetValue
              label="Used amount"
              currency={currentUser.base_currency}
              value={(budget.current_period as BudgetPeriod).current_amount}
              icon={<HandCoinsIcon className="text-dark-secondary size-4" />}
            />
          </div>
          <GaugeChart
            value={(budget.current_period as BudgetPeriod).current_amount}
            min={0}
            max={(budget.current_period as BudgetPeriod).planned_amount}
            label={"Budget usage"}
            formatValue={(value, isValue) =>
              isValue
                ? formatCurrency(currentUser.base_currency, value.toString())
                : formatAmountShort(value, currentUser.base_currency)
            }
          />
        </div>
      </div>
    </ContentBlock>
  );
}
