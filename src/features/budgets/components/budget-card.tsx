import type { Budget, BudgetPeriod } from "../types";

import { Link } from "@tanstack/react-router";
import { ChevronRightIcon, StarIcon } from "lucide-react";

import { But<PERSON> } from "~/components/ui/button";
import { <PERSON>, CardContent, <PERSON>Footer, CardHeader } from "~/components/ui/card";
import { useCurrentUser } from "~/features/auth/hooks";
import { formatCurrency } from "~/features/currencies/formatters";
import { formatDate } from "~/lib/formatters";

import BudgetDialog from "./budget-dialog";
import BudgetProgress from "./budget-progress";

interface Props {
  budget: Budget;
}

export default function BudgetCard({ budget }: Props) {
  const currentUser = useCurrentUser();

  // Calculate progress percentage and determine color
  const currentPeriod = budget.current_period ? (budget.current_period as BudgetPeriod) : null;

  const currentAmount = currentPeriod ? parseFloat(currentPeriod.current_amount) : 0;
  const plannedAmount = currentPeriod ? parseFloat(currentPeriod.planned_amount) : parseFloat(budget.amount);

  return (
    <Card className="gap-2 py-4" key={budget.id}>
      <CardHeader className="mx-4 border-b border-gray-200 px-0 pb-2 md:flex-row md:items-center md:justify-between">
        <div className="flex items-center gap-2">
          {budget.is_favorite && <StarIcon className="h-4 w-4 fill-yellow-400 text-yellow-400" />}
          <h4 className="text-gray-01 text-sm font-medium">{budget.name}</h4>
          {!budget.is_active && (
            <span className="rounded-full bg-gray-200 px-2 py-0.5 text-xs text-gray-600">Inactive</span>
          )}
        </div>
        <p className="text-gray-01 text-xs">Per {budget.period_type}</p>
      </CardHeader>
      <CardContent className="px-4">
        {currentPeriod ? (
          <div className="flex flex-col gap-2">
            <div className="flex justify-between">
              <p className="text-xs text-gray-500">
                {formatDate(currentPeriod.start_date)} - {formatDate(currentPeriod.end_date)}
              </p>
            </div>
            <div className="flex justify-between">
              <p className="text-dark text-lg leading-tight font-semibold">
                {formatCurrency(currentUser.base_currency, currentAmount.toString())}{" "}
                <span className="text-sm text-gray-500">
                  / {formatCurrency(currentUser.base_currency, plannedAmount.toString())}
                </span>
              </p>
            </div>
          </div>
        ) : (
          <div className="flex justify-between">
            <p className="text-dark text-lg leading-tight font-semibold">
              $0.00 <span className="text-sm text-gray-500">/ ${parseFloat(budget.amount).toFixed(2)}</span>
            </p>
          </div>
        )}
        <BudgetProgress value={currentAmount} max={plannedAmount} className="mt-2" />
      </CardContent>

      <CardFooter className="flex justify-between px-4 pt-2">
        <Link
          to={"/budgets/$budgetId"}
          params={{ budgetId: budget.id }}
          className="text-primary flex items-center text-sm hover:underline"
        >
          <span>Details</span>
          <ChevronRightIcon className="ml-1 size-4" />
        </Link>
        <div>
          <BudgetDialog budget={budget}>
            <Button variant="link" size="sm">
              Edit
            </Button>
          </BudgetDialog>
        </div>
      </CardFooter>
    </Card>
  );
}
