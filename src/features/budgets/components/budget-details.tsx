import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useNavigate } from "@tanstack/react-router";
import { toast } from "sonner";

import { deleteBudgetMutation, getBudgetsQueryKey } from "~/api/@tanstack/react-query.gen";
import Notification from "~/components/blocks/notification";
import BlockLoader from "~/components/elements/block-loader";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardFooter } from "~/components/ui/card";
import { useCurrentUser } from "~/features/auth/hooks";
import { useConfirmation } from "~/features/confirmations";
import { formatCurrency } from "~/features/currencies/formatters";

import { useBudget } from "../hooks";
import BudgetDialog from "./budget-dialog";

const periodTypeLabels: Record<string, string> = {
  week: "Weekly",
  month: "Monthly",
  year: "Yearly",
};

interface Props {
  budgetId: string;
}

export default function BudgetDetails({ budgetId }: Props) {
  const ask = useConfirmation();
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const currentUser = useCurrentUser();

  const { budget, isLoading, isError } = useBudget(budgetId);
  const { mutate: deleteBudget } = useMutation({
    ...deleteBudgetMutation(),
    onSuccess() {
      toast("Budget has been removed!");

      void queryClient.invalidateQueries({ queryKey: getBudgetsQueryKey() });
      void navigate({ to: "/budgets" });
    },
    onError() {
      toast("Error while removing budget");
    },
  });

  const handleRemove = async () => {
    if (!budget) return;

    const ok = await ask({
      title: "Remove Budget",
      description: `Are you sure you want to remove "${budget.name}"? This action cannot be undone.`,
      confirmText: "Remove",
      confirmVariant: "destructive",
    });

    if (ok) {
      deleteBudget({ path: { budget_id: budgetId } });
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent>
          <BlockLoader />
        </CardContent>
      </Card>
    );
  }

  if (isError) {
    return <Notification title="Error" message="Error loading budget details" />;
  }

  if (!budget) {
    return <Notification title="Budget not found" message="The budget you are looking for does not exist." />;
  }

  return (
    <Card>
      <CardContent>
        <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
          <div>
            <p className="text-sm text-gray-500">Budget Name</p>
            <p className="text-base font-bold text-gray-700">{budget.name}</p>
          </div>
          <div>
            <p className="text-sm text-gray-500">Period Type</p>
            <p className="text-base font-bold text-gray-700">{periodTypeLabels[budget.period_type]}</p>
          </div>
          <div>
            <p className="text-sm text-gray-500">Amount</p>
            <p className="text-base font-bold text-gray-700">
              {formatCurrency(currentUser.base_currency, budget.amount)}
              {budget.is_fixed_amount ? " (Fixed)" : " (% of Income)"}
            </p>
          </div>
          <div>
            <p className="text-sm text-gray-500">Status</p>
            <p className="text-base font-bold text-gray-700">{budget.is_active ? "Active" : "Inactive"}</p>
          </div>
          <div>
            <p className="text-sm text-gray-500">Favorite</p>
            <p className="text-base font-bold text-gray-700">{budget.is_favorite ? "Yes" : "No"}</p>
          </div>
          {budget.description && (
            <div className="md:col-span-3">
              <p className="text-sm text-gray-500">Description</p>
              <p className="text-base font-bold text-gray-700">{budget.description}</p>
            </div>
          )}
        </div>
      </CardContent>

      <CardFooter>
        <div className="flex gap-2">
          <BudgetDialog budget={budget}>
            <Button>Edit Details</Button>
          </BudgetDialog>
          <Button variant="ghost" onClick={handleRemove}>
            Remove
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
}
