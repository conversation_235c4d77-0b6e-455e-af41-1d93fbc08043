import { BudgetPeriod } from "~/api";
import Notification from "~/components/blocks/notification";
import BlockLoader from "~/components/elements/block-loader";
import { Card, CardContent } from "~/components/ui/card";
import { useCurrentUser } from "~/features/auth/hooks";
import { formatCurrency } from "~/features/currencies/formatters";
import { formatDate } from "~/lib/formatters";

import { useBudget } from "../hooks";
import BudgetProgress from "./budget-progress";

interface Props {
  budgetId: string;
}

export default function BudgetPeriodDetails({ budgetId }: Props) {
  const currentUser = useCurrentUser();
  const { budget, isLoading, isError } = useBudget(budgetId);

  if (isLoading) {
    return (
      <Card>
        <CardContent>
          <BlockLoader />
        </CardContent>
      </Card>
    );
  }

  if (isError) {
    return <Notification title="Error" message="Error loading budget details" />;
  }

  if (!budget) {
    return <Notification title="Budget not found" message="The budget you are looking for does not exist." />;
  }

  if (!budget.current_period) {
    return (
      <Card>
        <CardContent>
          <p className="text-sm text-gray-600 italic">No active budget period found.</p>
        </CardContent>
      </Card>
    );
  }

  const currentPeriod = budget.current_period as BudgetPeriod;
  const currentAmount = parseFloat(currentPeriod.current_amount);
  const plannedAmount = parseFloat(currentPeriod.planned_amount);
  const percentUsed = (currentAmount / plannedAmount) * 100;

  return (
    <Card>
      <CardContent className="py-6">
        <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
          <div>
            <p className="text-sm text-gray-500">Period Dates</p>
            <p className="text-base font-bold text-gray-700">
              {formatDate(currentPeriod.start_date)} - {formatDate(currentPeriod.end_date)}
            </p>
          </div>
          <div>
            <p className="text-sm text-gray-500">Planned Amount</p>
            <p className="text-base font-bold text-gray-700">
              {formatCurrency(currentUser.base_currency, currentPeriod.planned_amount)}
            </p>
          </div>
          <div>
            <p className="text-sm text-gray-500">Current Amount</p>
            <p className="text-base font-bold text-gray-700">
              {formatCurrency(currentUser.base_currency, currentPeriod.current_amount)}
            </p>
          </div>
        </div>

        <div className="mt-6">
          <div className="flex items-center justify-between">
            <p className="text-sm font-medium text-gray-700">Budget Usage</p>
            <p className="text-sm font-medium text-gray-700">{percentUsed.toFixed(1)}%</p>
          </div>
          <BudgetProgress value={currentAmount} max={plannedAmount} className="mt-2" />
        </div>
      </CardContent>
    </Card>
  );
}
