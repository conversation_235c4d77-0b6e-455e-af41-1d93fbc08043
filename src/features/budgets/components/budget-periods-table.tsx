import { BudgetPeriod } from "~/api";
import BlockLoader from "~/components/elements/block-loader";
import { Button } from "~/components/ui/button";
import { Card, CardContent } from "~/components/ui/card";
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "~/components/ui/table";
import { useCurrentUser } from "~/features/auth/hooks";
import { formatCurrency } from "~/features/currencies/formatters";
import { formatDate } from "~/lib/formatters";

import { useInfiniteBudgetPeriods } from "../hooks/useInfiniteBudgetPeriods";
import BudgetProgress from "./budget-progress";

interface Props {
  budgetId: string;
}

export default function BudgetPeriodsTable({ budgetId }: Props) {
  const { budgetPeriods, isLoading, fetchNextPage, hasNextPage, isFetchingNextPage } =
    useInfiniteBudgetPeriods(budgetId);

  const handleLoadMore = () => {
    void fetchNextPage();
  };

  // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
  if (isLoading) {
    return (
      <Card>
        <CardContent>
          <BlockLoader />
        </CardContent>
      </Card>
    );
  }

  if (!budgetPeriods.length) {
    return (
      <Card>
        <CardContent>
          <p className="text-sm text-gray-600 italic">No budget periods found.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <Card>
      <CardContent>
        <Table>
          <TableHeader>
            <TableRow>
              <TableHead>Period</TableHead>
              <TableHead>Planned Amount</TableHead>
              <TableHead>Current Amount</TableHead>
              <TableHead>Progress</TableHead>
            </TableRow>
          </TableHeader>
          <TableBody>
            {budgetPeriods.map((period) => (
              <BudgetPeriodRow key={period.id} period={period} />
            ))}
          </TableBody>
        </Table>

        {hasNextPage && (
          <div className="flex justify-center p-4">
            <Button variant="outline" onClick={handleLoadMore} disabled={isFetchingNextPage} className="min-w-[200px]">
              {isFetchingNextPage ? "Loading..." : "Load More"}
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
  );
}

interface BudgetPeriodRowProps {
  period: BudgetPeriod;
}

function BudgetPeriodRow({ period }: BudgetPeriodRowProps) {
  const currentUser = useCurrentUser();
  const currentAmount = parseFloat(period.current_amount);
  const plannedAmount = parseFloat(period.planned_amount);

  return (
    <TableRow>
      <TableCell>
        <span className="font-medium">
          {formatDate(period.start_date)} - {formatDate(period.end_date)}
        </span>
      </TableCell>
      <TableCell>{formatCurrency(currentUser.base_currency, period.planned_amount)}</TableCell>
      <TableCell>{formatCurrency(currentUser.base_currency, period.current_amount)}</TableCell>
      <TableCell className="w-1/4">
        <BudgetProgress value={currentAmount} max={plannedAmount} />
      </TableCell>
    </TableRow>
  );
}
