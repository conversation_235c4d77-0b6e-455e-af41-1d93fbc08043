import * as React from "react";

import { cn } from "~/lib/utils";

interface BudgetProgressProps extends React.ComponentProps<"div"> {
  value: number;
  max?: number;
  color?: "emerald" | "orange" | "rose" | "default";
}

export default function BudgetProgress({ className, value, max = 100, ...props }: BudgetProgressProps) {
  const percentage = Math.min(Math.max(0, (value / max) * 100), 100);

  return (
    <div className={cn("relative h-4 w-full overflow-hidden rounded-full bg-gray-200", className)} {...props}>
      <div
        className={cn("bg-primary h-full w-full flex-1 transition-all", {
          "bg-emerald-500": percentage < 75,
          "bg-orange-500": percentage >= 75 && percentage < 99,
          "bg-rose-500": percentage >= 99,
        })}
        style={{ transform: `translateX(-${(100 - percentage).toFixed(2)}%)` }}
      />
    </div>
  );
}
