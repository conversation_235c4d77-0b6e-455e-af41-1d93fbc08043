import type { Budget } from "../types";

import { useToggle } from "react-use";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import { deleteBudgetMutation, getBudgetsQueryKey } from "~/api/@tanstack/react-query.gen";
import { Button } from "~/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "~/components/ui/dialog";
import { useConfirmation } from "~/features/confirmations";

import BudgetForm from "./budget-form";

interface Props {
  budget?: Budget;
  children: React.ReactNode;
}

export default function BudgetDialog({ budget, children: trigger }: Props) {
  const [open, toggleOpen] = useToggle(false);
  const queryClient = useQueryClient();
  const ask = useConfirmation();

  const { mutate: deleteBudget } = useMutation({
    ...deleteBudgetMutation(),
    onSuccess() {
      void queryClient.invalidateQueries({ queryKey: getBudgetsQueryKey() });
      toast("Budget has been removed!");
      toggleOpen();
    },
    onError() {
      toast("Error while removing budget");
    },
  });

  const handleDelete = async () => {
    if (!budget) return;

    const ok = await ask({
      title: "Remove Budget",
      description: `Are you sure you want to remove "${budget.name}"? This action cannot be undone.`,
      confirmText: "Remove",
      confirmVariant: "destructive",
    });

    if (ok) {
      deleteBudget({ path: { budget_id: budget.id } });
    }
  };

  const isCreate = budget === undefined;

  return (
    <Dialog open={open} onOpenChange={toggleOpen}>
      <DialogTrigger asChild>{trigger}</DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{isCreate ? "Create budget" : `Edit ${budget.name}`}</DialogTitle>
          <DialogDescription>
            {isCreate ? "Create a new budget to track your spending." : "Update budget details"}
          </DialogDescription>
        </DialogHeader>

        <div className="py-4">
          <BudgetForm formId="budget-form" budget={budget} onSuccess={toggleOpen} />
        </div>

        <DialogFooter>
          {budget && (
            <Button type="button" variant="secondary" className="me-auto" onClick={handleDelete}>
              Delete
            </Button>
          )}

          <DialogClose asChild>
            <Button variant="ghost">Cancel</Button>
          </DialogClose>
          <Button type="submit" form="budget-form">
            {isCreate ? "Create budget" : "Save changes"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
