import type { Budget, BudgetData } from "../types";

import { useMemo } from "react";
import { useForm } from "react-hook-form";

import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import { createBudgetMutation, getBudgetsQueryKey, updateBudgetMutation } from "~/api/@tanstack/react-query.gen";
import InputBoolean from "~/components/inputs/input-boolean";
import InputSelect from "~/components/inputs/input-select";
import InputText from "~/components/inputs/input-text";
import { Form } from "~/components/ui/form";

import { BudgetSchema } from "../schemas";

interface Props {
  formId: string;
  budget?: Budget;
  onSuccess?: () => void;
}

const periodTypes = [
  { value: "week", label: "Weekly" },
  { value: "month", label: "Monthly" },
  { value: "year", label: "Yearly" },
];

export default function BudgetForm({ formId, budget, onSuccess }: Props) {
  const queryClient = useQueryClient();

  const isCreate = budget === undefined;
  const isUpdate = budget !== undefined;

  const defaultValues = useMemo<BudgetData>(
    () => ({
      name: budget?.name ?? "",
      amount: budget?.amount ?? "0",
      period_type: budget?.period_type ?? "month",
      is_fixed_amount: budget?.is_fixed_amount ?? true,
      is_active: budget?.is_active ?? true,
      is_favorite: budget?.is_favorite ?? false,
      description: budget?.description ?? "",
    }),
    [budget]
  );

  const { mutate: createBudget } = useMutation({
    ...createBudgetMutation(),
    onSuccess: () => {
      void queryClient.invalidateQueries({ queryKey: getBudgetsQueryKey() });
      toast("Budget created successfully!");
      onSuccess?.();
    },
  });

  const { mutate: updateBudget } = useMutation({
    ...updateBudgetMutation(),
    onSuccess: () => {
      void queryClient.invalidateQueries({ queryKey: getBudgetsQueryKey() });
      toast("Budget updated successfully!");
      onSuccess?.();
    },
  });

  const form = useForm({
    resolver: zodResolver<BudgetData>(BudgetSchema),
    defaultValues,
  });

  const onSubmit = form.handleSubmit((data) => {
    const body = { ...data, description: data.description || null };

    if (isCreate) {
      createBudget({ body });
    } else if (isUpdate) {
      updateBudget({ body, path: { budget_id: budget.id } });
    }
  });

  return (
    <Form {...form}>
      <form id={formId} onSubmit={onSubmit} className="space-y-4">
        <InputText
          control={form.control}
          name="name"
          label="Budget Name"
          placeholder="e.g., Groceries, Entertainment"
        />

        <InputText
          control={form.control}
          name="amount"
          label="Budget Amount"
          placeholder="0.00"
          type="number"
          step="0.01"
        />

        <InputSelect control={form.control} name="period_type" label="Period Type" values={periodTypes} />

        <InputBoolean
          control={form.control}
          name="is_fixed_amount"
          label="Fixed Amount"
          description="If enabled, the budget amount is a fixed value. If disabled, it's a percentage of income."
        />

        <InputBoolean
          control={form.control}
          name="is_active"
          label="Active"
          description="If disabled, this budget will be hidden from the main view."
        />

        <InputBoolean
          control={form.control}
          name="is_favorite"
          label="Favorite"
          description="Mark this budget as a favorite to highlight it."
        />

        <InputText
          control={form.control}
          name="description"
          label="Description"
          placeholder="Optional description"
          className="col-span-2"
        />
      </form>
    </Form>
  );
}
