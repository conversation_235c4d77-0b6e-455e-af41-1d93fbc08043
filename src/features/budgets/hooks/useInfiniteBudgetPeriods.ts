import { useMemo } from "react";

import { useInfiniteQuery } from "@tanstack/react-query";

import { getBudgetPeriodsInfiniteOptions } from "~/api/@tanstack/react-query.gen";

export function useInfiniteBudgetPeriods(budgetId: string, limit = 10) {
  const { data, isLoading, isError, error, fetchNextPage, hasNextPage, isFetchingNextPage } = useInfiniteQuery({
    ...getBudgetPeriodsInfiniteOptions({
      path: { budget_id: budgetId },
      query: {
        per_page: limit,
      },
    }),
    getNextPageParam: (lastPage) => {
      if (!lastPage.pagination) return undefined;
      const { page, pages } = lastPage.pagination;
      return page < pages ? page + 1 : undefined;
    },
  });

  // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
  const budgetPeriods = useMemo(() => data?.pages.flatMap((page) => page.items || []), [data]);

  const pagination = useMemo(() => {
    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
    if (!data?.pages.length) return undefined;

    return data.pages[data.pages.length - 1].pagination;
  }, [data]);

  return {
    budgetPeriods,
    pagination,
    isLoading,
    isError,
    error,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  };
}
