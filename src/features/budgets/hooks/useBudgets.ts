import { useMemo } from "react";

import { useQuery } from "@tanstack/react-query";

import { getBudgetsOptions } from "~/api/@tanstack/react-query.gen";

export function useBudgets() {
  const { data, isLoading, isError, error } = useQuery({
    ...getBudgetsOptions(),
  });

  const budgets = useMemo(() => data ?? [], [data]);
  const activeBudgets = useMemo(() => budgets.filter((budget) => budget.is_active), [budgets]);
  const inactiveBudgets = useMemo(() => budgets.filter((budget) => !budget.is_active), [budgets]);
  const favoriteBudgets = useMemo(() => budgets.filter((budget) => budget.is_favorite), [budgets]);

  return { isLoading, isError, error, budgets, activeBudgets, inactiveBudgets, favoriteBudgets };
}
