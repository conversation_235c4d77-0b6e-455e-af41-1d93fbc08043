import { useMemo } from "react";

import { useInfiniteQuery } from "@tanstack/react-query";

import { getTransactionsInfiniteOptions } from "~/api/@tanstack/react-query.gen";

export function useAccountTransactions(accountId: string, limit = 10) {
  const { data, isLoading, isError, error, fetchNextPage, hasNextPage, isFetchingNextPage } = useInfiniteQuery({
    ...getTransactionsInfiniteOptions({
      query: {
        account_id: accountId,
        per_page: limit,
      },
    }),
    getNextPageParam: (lastPage) => {
      if (!lastPage.pagination) return undefined;
      const { page, pages } = lastPage.pagination;
      return page < pages ? page + 1 : undefined;
    },
  });

  const transactions = useMemo(() => {
    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
    if (!data) return [];
    return data.pages.flatMap((page) => page.items || []);
  }, [data]);

  const pagination = useMemo(() => {
    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
    if (!data || !data.pages.length) return undefined;
    return data.pages[data.pages.length - 1].pagination;
  }, [data]);

  return {
    transactions,
    pagination,
    isLoading,
    isError,
    error,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  };
}
