import { useMemo } from "react";

import { useQuery } from "@tanstack/react-query";

import { getAccountsOptions } from "~/api/@tanstack/react-query.gen";

export function useAccounts() {
  const { data, isLoading, isError, error } = useQuery({
    ...getAccountsOptions(),
  });

  const accounts = useMemo(() => data ?? [], [data]);
  const groups = useMemo(() => {
    if (!data) return [];
    // a list of unique groups taken from accounts
    // we don't have separate relation and/or api for account groups
    return [...new Set(data.map((account) => account.group).filter((group) => group !== null))];
  }, [data]);

  const activeAccounts = useMemo(() => accounts.filter((account) => account.is_active), [accounts]);
  const archivedAccounts = useMemo(() => accounts.filter((account) => !account.is_active), [accounts]);

  return { isLoading, isError, error, accounts, groups, activeAccounts, archivedAccounts };
}
