import type { Account, UpdateAccountData } from "../types";

import { useForm } from "react-hook-form";

import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import { getAccountQueryKey, getAccountsQueryKey, updateAccountMutation } from "~/api/@tanstack/react-query.gen";
import InputAccountGroup from "~/components/inputs/input-account-group";
import InputBoolean from "~/components/inputs/input-boolean";
import InputCurrency from "~/components/inputs/input-currency";
import InputSelect from "~/components/inputs/input-select";
import InputText from "~/components/inputs/input-text";
import { Form } from "~/components/ui/form";
import { accountTypes } from "~/features/accounts/constants";
import { useAccounts } from "~/features/accounts/hooks";
import { UpdateAccountSchema } from "~/features/accounts/schemas";

interface Props {
  formId: string;
  account: Account;
  onSuccess?: () => void;
}

export default function AccountUpdateForm({ formId, account, onSuccess }: Props) {
  const queryClient = useQueryClient();

  const { groups } = useAccounts();

  const { mutate: updateAccount } = useMutation({
    ...updateAccountMutation(),
    onSuccess() {
      void queryClient.invalidateQueries({ queryKey: getAccountsQueryKey() });
      void queryClient.invalidateQueries({ queryKey: getAccountQueryKey({ path: { account_id: account.id } }) });
      toast("Account updated successfully.");
      onSuccess?.();
    },
  });

  const form = useForm({
    resolver: zodResolver<UpdateAccountData>(UpdateAccountSchema),
    defaultValues: {
      account_type: account.account_type,
      name: account.name,
      currency: account.currency,
      color: account.color,
      description: account.description || "",
      group: account.group,
      is_active: account.is_active,
      overdraft_limit: account.overdraft_limit,
    },
  });

  const onSubmit = form.handleSubmit((data) => {
    const body = { ...data, group: data.group?.trim() || null, description: data.description?.trim() || null };
    updateAccount({ body, path: { account_id: account.id } });
  });

  return (
    <Form {...form}>
      <form id={formId} onSubmit={onSubmit} className="grid grid-cols-1 gap-4 md:grid-cols-4">
        <InputAccountGroup
          control={form.control}
          groups={groups}
          name="group"
          label="Group"
          className="md:col-span-3"
        />
        <InputText control={form.control} name="color" label="Color" type="color" />
        <InputText control={form.control} name="name" label="Name" className="md:col-span-2" />
        <InputCurrency control={form.control} name="currency" label="Currency" className="md:col-span-2" />
        <InputSelect
          control={form.control}
          name="account_type"
          values={accountTypes}
          label="Account type"
          className="md:col-span-2"
        />
        <InputText
          control={form.control}
          name="overdraft_limit"
          label="Overdraft limit"
          className="md:col-span-2"
          type="number"
          inputMode="numeric"
          placeholder="0.00"
          step={0.01}
        />
        <InputText control={form.control} name="description" label="Description" className="md:col-span-4" />

        <div className="md:col-span-4">
          <InputBoolean
            control={form.control}
            name="is_active"
            label="Is active?"
            description="Uncheck to archive this account"
          />
        </div>
      </form>
    </Form>
  );
}
