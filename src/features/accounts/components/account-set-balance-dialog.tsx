import type { Account, SetAccountBalanceData } from "../types";

import { useForm } from "react-hook-form";
import { useToggle } from "react-use";

import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import { getAccountQueryKey, setAccountBalanceMutation } from "~/api/@tanstack/react-query.gen";
import InputText from "~/components/inputs/input-text";
import { Button } from "~/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "~/components/ui/dialog";
import { Form } from "~/components/ui/form";
import { formatCurrency } from "~/features/currencies/formatters";

import { SetAccountBalanceSchema } from "../schemas";

interface Props {
  account: Account;
  children: React.ReactNode;
}

export default function AccountSetBalanceDialog({ account, children: trigger }: Props) {
  const [open, toggleOpen] = useToggle(false);
  const queryClient = useQueryClient();

  const { mutate: setBalance } = useMutation({
    ...setAccountBalanceMutation(),
    onSuccess() {
      // Invalidate account queries
      void queryClient.invalidateQueries({ queryKey: getAccountQueryKey({ path: { account_id: account.id } }) });

      // Invalidate transaction queries
      void queryClient.invalidateQueries({ queryKey: [{ _id: "getTransactions" }] });

      toast("Account balance updated successfully.");
      toggleOpen();
    },
    onError() {
      toast("Error updating account balance.");
    },
  });

  const form = useForm<SetAccountBalanceData>({
    resolver: zodResolver(SetAccountBalanceSchema),
    defaultValues: {
      balance: account.current_balance,
    },
  });

  const onSubmit = form.handleSubmit((data) => {
    setBalance({ body: data, path: { account_id: account.id } });
  });

  return (
    <Dialog open={open} onOpenChange={toggleOpen}>
      <DialogTrigger asChild>{trigger}</DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>Set Balance for {account.name}</DialogTitle>
          <DialogDescription>
            Update the current balance for this account. This will create a transaction to adjust the balance.
          </DialogDescription>

          <div className="py-4">
            <Form {...form}>
              <form id="set-balance-form" onSubmit={onSubmit} className="space-y-4">
                <div className="flex flex-col gap-2">
                  <p className="text-sm text-gray-500">Current Balance</p>
                  <p className="text-base font-bold text-gray-700">
                    {formatCurrency(account.currency, account.current_balance)}
                  </p>
                </div>
                <InputText
                  control={form.control}
                  name="balance"
                  label="New Balance"
                  type="number"
                  inputMode="numeric"
                  placeholder="0.00"
                  step={0.01}
                />
              </form>
            </Form>
          </div>

          <DialogFooter>
            <DialogClose asChild>
              <Button variant="ghost">Cancel</Button>
            </DialogClose>
            <Button type="submit" form="set-balance-form">
              Update Balance
            </Button>
          </DialogFooter>
        </DialogHeader>
      </DialogContent>
    </Dialog>
  );
}
