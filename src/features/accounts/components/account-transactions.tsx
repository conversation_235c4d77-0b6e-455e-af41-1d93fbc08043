import Notification from "~/components/blocks/notification";
import BlockLoader from "~/components/elements/block-loader";
import { Button } from "~/components/ui/button";
import { Card, CardContent } from "~/components/ui/card";
import { useAccountTransactions } from "~/features/accounts/hooks";
import TransactionTable from "~/features/transactions/components/transaction-table";

interface Props {
  accountId: string;
}

export default function AccountTransactions({ accountId }: Props) {
  const { transactions, isLoading, isError, fetchNextPage, hasNextPage, isFetchingNextPage } =
    useAccountTransactions(accountId);

  const handleLoadMore = () => {
    void fetchNextPage();
  };

  // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
  if (isLoading) {
    return (
      <Card>
        <CardContent>
          <BlockLoader />
        </CardContent>
      </Card>
    );
  }

  if (isError) {
    return <Notification title="Error" message="Error loading transactions" />;
  }

  if (!transactions.length) {
    return (
      <Card>
        <CardContent className="flex min-h-24 items-center justify-center">
          <p className="text-center text-sm text-gray-600">No transactions to show for this account.</p>
        </CardContent>
      </Card>
    );
  }

  return (
    <div className="rounded-xl bg-white p-6">
      <TransactionTable transactions={transactions} />

      {hasNextPage && (
        <div className="mt-4 flex justify-center">
          <Button variant="outline" onClick={handleLoadMore} disabled={isFetchingNextPage} className="min-w-[200px]">
            {isFetchingNextPage ? "Loading..." : "Load More"}
          </Button>
        </div>
      )}
    </div>
  );
}
