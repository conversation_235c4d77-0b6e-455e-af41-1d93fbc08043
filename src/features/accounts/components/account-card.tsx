import type { Account } from "../types";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Link } from "@tanstack/react-router";
import { ChevronRightIcon } from "lucide-react";
import { toast } from "sonner";

import { getAccountsQueryKey, removeAccountMutation } from "~/api/@tanstack/react-query.gen";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardFooter, CardHeader } from "~/components/ui/card";
import { useConfirmation } from "~/features/confirmations";
import { formatCurrency } from "~/features/currencies/formatters";

import AccountDialog from "./account-dialog";

interface Props {
  account: Account;
}

export default function AccountCard({ account }: Props) {
  const ask = useConfirmation();
  const queryClient = useQueryClient();

  const { mutate: removeAccount } = useMutation({
    ...removeAccountMutation(),
    onSuccess() {
      void queryClient.invalidateQueries({ queryKey: getAccountsQueryKey() });
      toast("Account has been removed!");
    },
    onError() {
      toast("Error while removing account");
    },
  });

  const handleRemove = async () => {
    const ok = await ask({
      title: "Remove Account",
      description: `Are you sure you want to remove "${account.name}"? This action cannot be undone.`,
      confirmText: "Remove",
      confirmVariant: "destructive",
    });

    if (ok) {
      removeAccount({ path: { account_id: account.id } });
    }
  };

  return (
    <Card
      className={`gap-2 border-l-4 py-4 ${!account.is_active ? "opacity-70" : ""}`}
      key={account.id}
      style={{ borderColor: account.color }}
    >
      <CardHeader className="mx-4 border-b border-gray-200 px-0 pb-2 md:flex-row md:items-center md:justify-between">
        <div className="flex items-center gap-2">
          <h4 className="text-gray-02 text-sm font-medium">{account.name}</h4>
          {!account.is_active && (
            <span className="rounded-full bg-gray-200 px-2 py-0.5 text-xs text-gray-600">Archived</span>
          )}
        </div>
        <p className="text-gray-01 text-xs" style={{ color: account.color }}>
          {account.group || "-"}
        </p>
      </CardHeader>
      <CardContent className="px-4">
        <div>
          <p className="text-dark text-lg leading-tight font-semibold">
            {formatCurrency(account.currency, account.current_balance)}
          </p>
          <p className="text-gray-03 text-xs">Current balance</p>
        </div>
      </CardContent>
      <CardFooter className="px-4 pt-2">
        <Button variant="link" size="sm" className="-ms-3" onClick={handleRemove}>
          Remove
        </Button>

        <div className="ms-auto flex items-center gap-1.5">
          <AccountDialog account={account}>
            <Button variant="link" size="sm">
              Edit
            </Button>
          </AccountDialog>

          <Button size="sm" asChild>
            <Link to="/accounts/$accountId" params={{ accountId: account.id }}>
              Details <ChevronRightIcon />
            </Link>
          </Button>
        </div>
      </CardFooter>
    </Card>
  );
}
