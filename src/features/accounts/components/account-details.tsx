import { useMutation, useQueryClient } from "@tanstack/react-query";
import { useNavigate } from "@tanstack/react-router";
import { toast } from "sonner";

import { getAccountsQueryKey, removeAccountMutation } from "~/api/@tanstack/react-query.gen";
import Notification from "~/components/blocks/notification";
import BlockLoader from "~/components/elements/block-loader";
import { Button } from "~/components/ui/button";
import { Card, CardContent, CardFooter } from "~/components/ui/card";
import { useAccount } from "~/features/accounts/hooks";
import { useCurrentUser } from "~/features/auth/hooks";
import { useConfirmation } from "~/features/confirmations";
import { formatCurrency } from "~/features/currencies/formatters";

import AccountDialog from "./account-dialog";
import AccountSetBalanceDialog from "./account-set-balance-dialog";

const accountTypeLabels: Record<string, string> = {
  cash: "Cash",
  card: "Card",
  bank_account: "Bank Account",
  savings: "Savings",
  loan: "Loan",
  other: "Other",
};

interface Props {
  accountId: string;
}

export default function AccountDetails({ accountId }: Props) {
  const ask = useConfirmation();
  const queryClient = useQueryClient();
  const navigate = useNavigate();
  const currentUser = useCurrentUser();

  const { account, isLoading, isError } = useAccount(accountId);
  const { mutate: removeAccount } = useMutation({
    ...removeAccountMutation(),
    onSuccess() {
      toast("Account has been removed!");

      void queryClient.invalidateQueries({ queryKey: getAccountsQueryKey() });
      void navigate({ to: "/accounts" });
    },
    onError() {
      toast("Error while removing account");
    },
  });

  const handleRemove = async () => {
    if (!account) return;

    const ok = await ask({
      title: "Remove Account",
      description: `Are you sure you want to remove "${account.name}"? This action cannot be undone.`,
      confirmText: "Remove",
      confirmVariant: "destructive",
    });

    if (ok) {
      removeAccount({ path: { account_id: accountId } });
    }
  };

  if (isLoading) {
    return (
      <Card>
        <CardContent>
          <BlockLoader />
        </CardContent>
      </Card>
    );
  }

  if (isError) {
    return <Notification title="Error" message="Error loading account details" />;
  }

  if (!account) {
    return <Notification title="Account not found" message="The account you are looking for does not exist." />;
  }

  const hasOverdraftLimit = account.overdraft_limit && !!Number(account.overdraft_limit);

  return (
    <Card>
      <CardContent>
        <div className="grid grid-cols-1 gap-6 md:grid-cols-3">
          <div>
            <p className="text-sm text-gray-500">Account Name</p>
            <p className="text-base font-bold text-gray-700">{account.name}</p>
          </div>
          <div>
            <p className="text-sm text-gray-500">Group/Bank Name</p>
            <p className="text-base font-bold text-gray-700">{account.group || "-"}</p>
          </div>
          <div>
            <p className="text-sm text-gray-500">Account Type</p>
            <p className="text-base font-bold text-gray-700">{accountTypeLabels[account.account_type]}</p>
          </div>
          <div>
            <p className="text-sm text-gray-500">Current Balance</p>
            <p className="text-base font-bold text-gray-700">
              {formatCurrency(account.currency, account.current_balance)}
              {currentUser.base_currency !== account.currency && (
                <span className="text-sm font-normal text-gray-500">
                  {" "}
                  ({formatCurrency(currentUser.base_currency, account.base_current_balance)})
                </span>
              )}
            </p>
          </div>
          <div>
            <p className="text-sm text-gray-500">Opening Balance</p>
            <p className="text-base font-bold text-gray-700">
              {formatCurrency(account.currency, account.opening_balance)}
              {currentUser.base_currency !== account.currency && (
                <span className="text-sm font-normal text-gray-500">
                  {" "}
                  ({formatCurrency(currentUser.base_currency, account.base_opening_balance)})
                </span>
              )}
            </p>
          </div>
          {hasOverdraftLimit && (
            <div>
              <p className="text-sm text-gray-500">Overdraft Limit</p>
              <p className="text-base font-bold text-gray-700">
                {formatCurrency(account.currency, account.overdraft_limit)}
              </p>
            </div>
          )}
        </div>
      </CardContent>

      <CardFooter>
        <div className="flex gap-2">
          <AccountDialog account={account}>
            <Button>Edit Details</Button>
          </AccountDialog>
          <Button variant="ghost" onClick={handleRemove}>
            Remove
          </Button>
        </div>

        <div className="ms-auto">
          <AccountSetBalanceDialog account={account}>
            <Button variant="secondary">Set Balance</Button>
          </AccountSetBalanceDialog>
        </div>
      </CardFooter>
    </Card>
  );
}
