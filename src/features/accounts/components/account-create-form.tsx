import type { CreateAccountData } from "../types";

import { useEffect } from "react";
import { useForm } from "react-hook-form";

import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import { createAccountMutation, getAccountsQueryKey } from "~/api/@tanstack/react-query.gen";
import InputAccountGroup from "~/components/inputs/input-account-group";
import InputCurrency from "~/components/inputs/input-currency";
import InputText from "~/components/inputs/input-text";
import { Form } from "~/components/ui/form";
import { useAccounts } from "~/features/accounts/hooks";
import { useCurrentUser } from "~/features/auth/hooks";

import { CreateAccountSchema } from "../schemas";

interface Props {
  formId: string;
  onSuccess?: () => void;
}

const defaultColor = "#4260a5";

export default function AccountCreateForm({ formId, onSuccess }: Props) {
  const user = useCurrentUser();
  const queryClient = useQueryClient();

  const { groups, accounts } = useAccounts();

  const { mutate: createAccount } = useMutation({
    ...createAccountMutation(),
    onSuccess: () => {
      void queryClient.invalidateQueries({ queryKey: getAccountsQueryKey() });
      toast("Account created successfully!");
      onSuccess?.();
    },
  });

  const form = useForm({
    resolver: zodResolver<CreateAccountData>(CreateAccountSchema),
    defaultValues: {
      group: "",
      name: "",
      currency: user.base_currency,
      opening_balance: "0",
      description: "",
      color: defaultColor,
      overdraft_limit: "0",
    },
  });
  const { watch, setValue } = form;

  useEffect(() => {
    const { unsubscribe } = watch((value, { name, type }) => {
      if (name !== "group" || type !== "change") return;

      if (value.group === "") {
        setValue("color", defaultColor);
      } else {
        const groupColor = accounts.find((account) => account.group === value.group)?.color || defaultColor;
        setValue("color", groupColor);
      }
    });

    return () => {
      unsubscribe();
    };
  }, [watch, setValue, accounts]);

  const onSubmit = form.handleSubmit((data) => {
    const body = { ...data, group: data.group?.trim() || null, description: data.description?.trim() || null };
    createAccount({ body });
  });

  return (
    <Form {...form}>
      <form id={formId} onSubmit={onSubmit} className="grid grid-cols-1 gap-4 md:grid-cols-4">
        <InputAccountGroup
          control={form.control}
          groups={groups}
          name="group"
          label="Group"
          className="md:col-span-3"
        />
        <InputText control={form.control} name="color" label="Color" type="color" />
        <InputText control={form.control} name="name" label="Name" className="md:col-span-2" />
        <InputCurrency control={form.control} name="currency" label="Currency" className="md:col-span-2" />
        <InputText
          control={form.control}
          name="opening_balance"
          label="Opening balance"
          className="md:col-span-2"
          type="number"
          inputMode="numeric"
          // pattern="\d*"
          placeholder="0.00"
          step={0.01}
        />
        <InputText
          control={form.control}
          name="overdraft_limit"
          label="Overdraft limit"
          className="md:col-span-2"
          type="number"
          inputMode="numeric"
          // pattern="\d*"
          placeholder="0.00"
          step={0.01}
        />
        <InputText control={form.control} name="description" label="Description" className="md:col-span-4" />
      </form>
    </Form>
  );
}
