import { useCallback, useState } from "react";

import { ChevronDownIcon } from "lucide-react";

import { Button } from "~/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";

interface Props {
  groups: string[];
  onChange: (group: string | null) => void;
}

export default function AccountGroupFilter(props: Props) {
  const { groups, onChange } = props;

  const [selectedGroup, setSelectedGroup] = useState<string | null>(null);

  const handleChange = useCallback(
    (value: string | null) => {
      setSelectedGroup(value);
      onChange(value);
    },
    [onChange]
  );

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm" className="ml-2">
          {selectedGroup ? `Group: ${selectedGroup}` : "All Groups"}
          <ChevronDownIcon className="ml-2 h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end">
        <DropdownMenuLabel>Filter by Group</DropdownMenuLabel>
        <DropdownMenuSeparator />
        <DropdownMenuGroup>
          <DropdownMenuItem
            onClick={() => {
              handleChange(null);
            }}
          >
            All Groups
          </DropdownMenuItem>
          {groups.map((group) => (
            <DropdownMenuItem
              key={group}
              onClick={() => {
                handleChange(group);
              }}
            >
              {group}
            </DropdownMenuItem>
          ))}
        </DropdownMenuGroup>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
