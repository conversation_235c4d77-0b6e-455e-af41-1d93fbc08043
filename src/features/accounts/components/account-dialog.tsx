import type { Account } from "../types";

import { useToggle } from "react-use";

import { Button } from "~/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  Di<PERSON>Header,
  <PERSON><PERSON><PERSON>itle,
  DialogTrigger,
} from "~/components/ui/dialog";

import AccountCreateForm from "./account-create-form";
import AccountUpdateForm from "./account-update-form";

interface Props {
  account?: Account;
  children: React.ReactNode;
}

export default function AccountDialog({ account, children: trigger }: Props) {
  const [open, toggleOpen] = useToggle(false);

  const isCreate = account === undefined;
  const isUpdate = account !== undefined;

  return (
    <Dialog open={open} onOpenChange={toggleOpen}>
      <DialogTrigger asChild>{trigger}</DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{isCreate ? "Create account" : `Edit ${account.name}`}</DialogTitle>
          <DialogDescription>
            {isCreate ? "Create a new account to track your finances." : "Update account details"}
          </DialogDescription>

          <div className="py-4">
            {isCreate && <AccountCreateForm formId="account-form" onSuccess={toggleOpen} />}
            {isUpdate && <AccountUpdateForm formId="account-form" account={account} onSuccess={toggleOpen} />}
          </div>

          <DialogFooter>
            <DialogClose asChild>
              <Button variant="ghost">Cancel</Button>
            </DialogClose>
            <Button type="submit" form="account-form">
              {isCreate ? "Create account" : "Save changes"}
            </Button>
          </DialogFooter>
        </DialogHeader>
      </DialogContent>
    </Dialog>
  );
}
