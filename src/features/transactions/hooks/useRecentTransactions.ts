import { useMemo } from "react";

import { useQuery } from "@tanstack/react-query";

import { getTransactionsOptions } from "~/api/@tanstack/react-query.gen";

export function useRecentTransactions(limit = 5) {
  const { data, isLoading, isError, error } = useQuery({
    ...getTransactionsOptions({
      query: {
        page: 1,
        per_page: limit,
      },
    }),
  });

  const transactions = useMemo(() => data?.items ?? [], [data]);

  return { transactions, isLoading, isError, error };
}
