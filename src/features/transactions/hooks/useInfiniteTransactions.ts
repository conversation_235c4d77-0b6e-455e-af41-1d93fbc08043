import { useMemo } from "react";

import { useInfiniteQuery } from "@tanstack/react-query";

import { getTransactionsInfiniteOptions } from "~/api/@tanstack/react-query.gen";

export interface TransactionFilters {
  transaction_type?: string | null;
  category_id?: string | null;
  account_id?: string | null;
}

export function useInfiniteTransactions(filters?: TransactionFilters, limit = 25) {
  const { data, isLoading, isError, error, fetchNextPage, hasNextPage, isFetchingNextPage } = useInfiniteQuery({
    ...getTransactionsInfiniteOptions({
      query: {
        per_page: limit,
        transaction_type: filters?.transaction_type || undefined,
        category_id: filters?.category_id || undefined,
        account_id: filters?.account_id || undefined,
      },
    }),
    getNextPageParam: (lastPage) => {
      if (!lastPage.pagination) return undefined;
      const { page, pages } = lastPage.pagination;
      return page < pages ? page + 1 : undefined;
    },
  });

  // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
  const transactions = useMemo(() => data?.pages.flatMap((page) => page.items || []), [data]);

  const pagination = useMemo(() => {
    // eslint-disable-next-line @typescript-eslint/no-unnecessary-condition
    if (!data?.pages.length) return undefined;

    return data.pages[data.pages.length - 1].pagination;
  }, [data]);

  return {
    transactions,
    pagination,
    isLoading,
    isError,
    error,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  };
}
