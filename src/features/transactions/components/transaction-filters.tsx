import { useEffect, useState } from "react";

import { ChevronDownIcon, FilterIcon } from "lucide-react";

import { Button } from "~/components/ui/button";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuGroup,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "~/components/ui/dropdown-menu";
import { useAccounts } from "~/features/accounts/hooks";
import { useCategories } from "~/features/categories/hooks";

import { TRANSACTION_TYPE_LABELS, TRANSACTION_TYPES } from "../constants";

export interface TransactionFilters {
  transaction_type?: string | null;
  category_id?: string | null;
  account_id?: string | null;
}

interface Props {
  onChange: (filters: TransactionFilters) => void;
}

export default function TransactionFilters({ onChange }: Props) {
  const { accounts } = useAccounts();
  const { categories } = useCategories();

  const [filters, setFilters] = useState<TransactionFilters>({
    transaction_type: null,
    category_id: null,
    account_id: null,
  });

  // Count active filters
  const activeFiltersCount = Object.values(filters).filter(Boolean).length;

  // Update parent component when filters change
  useEffect(() => {
    onChange(filters);
  }, [filters, onChange]);

  const handleFilterChange = (key: keyof TransactionFilters, value: string | null) => {
    setFilters((prev) => ({
      ...prev,
      [key]: value,
    }));
  };

  const clearFilters = () => {
    setFilters({
      transaction_type: null,
      category_id: null,
      account_id: null,
    });
  };

  return (
    <DropdownMenu>
      <DropdownMenuTrigger asChild>
        <Button variant="outline" size="sm" className="relative">
          <FilterIcon className="mr-2 h-4 w-4" />
          Filters
          {activeFiltersCount > 0 && (
            <span className="bg-primary text-primary-foreground absolute -top-1 -right-1 flex h-4 w-4 items-center justify-center rounded-full text-[10px]">
              {activeFiltersCount}
            </span>
          )}
          <ChevronDownIcon className="ml-2 h-4 w-4" />
        </Button>
      </DropdownMenuTrigger>
      <DropdownMenuContent align="end" className="w-[300px]">
        <DropdownMenuLabel>Filter Transactions</DropdownMenuLabel>
        <DropdownMenuSeparator />

        {/* Transaction Type Filter */}
        <DropdownMenuGroup>
          <DropdownMenuLabel className="text-muted-foreground text-xs font-normal">Transaction Type</DropdownMenuLabel>
          <div className="px-2 py-1.5">
            <div className="flex flex-wrap gap-1">
              {TRANSACTION_TYPES.map((type) => {
                const currentTypes = filters.transaction_type?.split(",").filter(Boolean) || [];
                const isActive = currentTypes.includes(type);

                return (
                  <Button
                    key={type}
                    variant={isActive ? "default" : "outline"}
                    size="sm"
                    className="h-6 text-xs"
                    onClick={() => {
                      if (isActive) {
                        // Remove type
                        const newTypes = currentTypes.filter((t) => t !== type);
                        handleFilterChange("transaction_type", newTypes.length ? newTypes.join(",") : null);
                      } else {
                        // Add type
                        const newTypes = [...currentTypes, type];
                        handleFilterChange("transaction_type", newTypes.join(","));
                      }
                    }}
                  >
                    {TRANSACTION_TYPE_LABELS[type]}
                  </Button>
                );
              })}
            </div>
          </div>
        </DropdownMenuGroup>

        <DropdownMenuSeparator />

        {/* Category Filter */}
        <DropdownMenuGroup>
          <DropdownMenuLabel className="text-muted-foreground text-xs font-normal">Category</DropdownMenuLabel>
          <div className="px-2 py-1.5">
            <div className="flex max-h-24 flex-wrap gap-1 overflow-y-auto">
              {categories.map((category) => {
                const currentCategories = filters.category_id?.split(",").filter(Boolean) || [];
                const isActive = currentCategories.includes(category.id);

                return (
                  <Button
                    key={category.id}
                    variant={isActive ? "default" : "outline"}
                    size="sm"
                    className="h-6 text-xs"
                    onClick={() => {
                      if (isActive) {
                        // Remove category
                        const newCategories = currentCategories.filter((id) => id !== category.id);
                        handleFilterChange("category_id", newCategories.length ? newCategories.join(",") : null);
                      } else {
                        // Add category
                        const newCategories = [...currentCategories, category.id];
                        handleFilterChange("category_id", newCategories.join(","));
                      }
                    }}
                  >
                    <span
                      className="mr-1 inline-block h-2 w-2 rounded-full"
                      style={{ backgroundColor: category.color }}
                    />
                    {category.name}
                  </Button>
                );
              })}
            </div>
          </div>
        </DropdownMenuGroup>

        <DropdownMenuSeparator />

        {/* Account Filter */}
        <DropdownMenuGroup>
          <DropdownMenuLabel className="text-muted-foreground text-xs font-normal">Account</DropdownMenuLabel>
          <div className="px-2 py-1.5">
            <div className="flex max-h-24 flex-wrap gap-1 overflow-y-auto">
              {accounts.map((account) => {
                const currentAccounts = filters.account_id?.split(",").filter(Boolean) || [];
                const isActive = currentAccounts.includes(account.id);

                return (
                  <Button
                    key={account.id}
                    variant={isActive ? "default" : "outline"}
                    size="sm"
                    className="h-6 text-xs"
                    onClick={() => {
                      if (isActive) {
                        // Remove account
                        const newAccounts = currentAccounts.filter((id) => id !== account.id);
                        handleFilterChange("account_id", newAccounts.length ? newAccounts.join(",") : null);
                      } else {
                        // Add account
                        const newAccounts = [...currentAccounts, account.id];
                        handleFilterChange("account_id", newAccounts.join(","));
                      }
                    }}
                  >
                    <span
                      className="mr-1 inline-block h-2 w-2 rounded-full"
                      style={{ backgroundColor: account.color }}
                    />
                    {account.name}
                  </Button>
                );
              })}
            </div>
          </div>
        </DropdownMenuGroup>

        <DropdownMenuSeparator />

        {/* Clear Filters */}
        <DropdownMenuItem className="justify-center text-center font-medium" onClick={clearFilters}>
          Clear All Filters
        </DropdownMenuItem>
      </DropdownMenuContent>
    </DropdownMenu>
  );
}
