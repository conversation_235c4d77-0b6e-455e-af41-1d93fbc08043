import type { Transaction, TransactionData, TransactionType } from "../types";

import { useForm } from "react-hook-form";

import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation } from "@tanstack/react-query";
import { toast } from "sonner";

import { createTransactionMutation, updateTransactionMutation } from "~/api/@tanstack/react-query.gen";
import InputSelect from "~/components/inputs/input-select";
import InputText from "~/components/inputs/input-text";
import { Form } from "~/components/ui/form";
import { ToggleGroup, ToggleGroupItem } from "~/components/ui/toggle-group";
import { useAccounts } from "~/features/accounts/hooks";
import { useCategories } from "~/features/categories/hooks";

import { TRANSACTION_TYPE_LABELS, TRANSACTION_TYPES } from "../constants";
import { TransactionSchema } from "../schemas";

interface Props {
  formId: string;
  transaction?: Transaction;
  onSuccess?: () => void;
  defaultValues?: TransactionData;
}

export default function TransactionForm({ formId, transaction, onSuccess, defaultValues }: Props) {
  const { expenseCategories, incomeCategories } = useCategories();
  const { activeAccounts: accounts } = useAccounts();

  const isCreate = transaction === undefined;
  const isUpdate = transaction !== undefined;

  const { mutate: createTransaction } = useMutation({
    ...createTransactionMutation(),
    onSuccess: () => {
      toast("Transaction created successfully!");
      onSuccess?.();
    },
  });

  const { mutate: updateTransaction } = useMutation({
    ...updateTransactionMutation(),
    onSuccess: () => {
      toast("Transaction updated successfully!");
      onSuccess?.();
    },
  });

  const form = useForm<TransactionData>({
    // @ts-expect-error Wrong type generation
    resolver: zodResolver(TransactionSchema),
    defaultValues,
  });

  const transactionType = form.watch("transaction_type") as TransactionType;
  const isTransfer = transactionType === "transfer";
  const isExpense = transactionType === "expense";

  const onSubmit = form.handleSubmit((data) => {
    const body = { ...data, description: data.description || null, category_id: data.category_id || null };

    if (isCreate) {
      createTransaction({ body });
    } else if (isUpdate) {
      updateTransaction({ body, path: { transaction_id: transaction.id } });
    }
  });

  return (
    <Form {...form}>
      <form id={formId} onSubmit={onSubmit} className="grid grid-cols-1 gap-4">
        <div className="flex flex-col gap-2">
          <ToggleGroup
            type="single"
            value={transactionType}
            onValueChange={(value: TransactionType | "") => {
              if (value) form.setValue("transaction_type", value);
            }}
            className="justify-start"
          >
            {TRANSACTION_TYPES.map((type) => (
              <ToggleGroupItem key={type} value={type} aria-label={TRANSACTION_TYPE_LABELS[type]} size="sm">
                {TRANSACTION_TYPE_LABELS[type]}
              </ToggleGroupItem>
            ))}
          </ToggleGroup>
        </div>

        <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
          <InputText control={form.control} name="transaction_date" label="Date" type="date" />
          <div>
            {!isTransfer && (
              <InputSelect
                control={form.control}
                name="category_id"
                label="Category"
                values={(isExpense ? expenseCategories : incomeCategories).map((category) => ({
                  value: category.id,
                  label: category.name,
                }))}
              />
            )}
          </div>

          <InputSelect
            control={form.control}
            name="account_id"
            label="Account"
            values={accounts.map((account) => ({
              value: account.id,
              label: account.name,
            }))}
          />
          <InputText control={form.control} name="amount" label="Amount" type="number" step="0.01" />

          {isTransfer && (
            <>
              <InputSelect
                control={form.control}
                name="account_to_id"
                label="To Account"
                values={accounts
                  .filter((account) => account.id !== form.getValues("account_id"))
                  .map((account) => ({
                    value: account.id,
                    label: account.name,
                  }))}
              />
              <InputText control={form.control} name="amount_to" label="Amount To" type="number" step="0.01" />
            </>
          )}

          <InputText control={form.control} name="description" label="Description" className="md:col-span-2" />
        </div>
      </form>
    </Form>
  );
}
