import { Repeat2Icon as TransferIcon } from "lucide-react";

import { Category, Transaction } from "~/api";
import { CategoryIcon } from "~/features/categories/components";

interface Props {
  transaction: Transaction;
}

export default function TransactionIcon({ transaction }: Props) {
  const isTransfer = transaction.transaction_type === "transfer";
  return (
    <span className="block rounded-md bg-gray-100 p-2">
      {isTransfer ? (
        <TransferIcon className="text-gray-01 size-6" />
      ) : (
        <CategoryIcon category={transaction.category as Category | null} />
      )}
    </span>
  );
}
