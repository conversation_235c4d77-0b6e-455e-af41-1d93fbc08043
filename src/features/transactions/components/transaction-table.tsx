import { Transaction } from "~/api";
import { Table, TableBody, TableHead, TableHeader, TableRow } from "~/components/ui/table";

import TransactionDialog from "./transaction-dialog";
import TransactionTableItem from "./transaction-table-item";

interface TransactionTableProps {
  transactions: Transaction[];
}

export default function TransactionTable({ transactions }: TransactionTableProps) {
  if (!transactions.length)
    return (
      <div className="flex min-h-52 flex-col items-center justify-center gap-4">
        <p className="text-center text-sm text-gray-600">
          No transactions to show. Start by creating
          <TransactionDialog>
            <button className="text-primary inline-block cursor-pointer pl-1.5 hover:underline">
              a new transaction
            </button>
          </TransactionDialog>
        </p>
      </div>
    );

  return (
    <Table>
      <TableHeader>
        <TableRow>
          <TableHead>Transaction</TableHead>
          <TableHead>Date</TableHead>
          <TableHead>Account</TableHead>
          <TableHead className="text-right">Amount</TableHead>
          <TableHead></TableHead>
        </TableRow>
      </TableHeader>
      <TableBody>
        {transactions.map((transaction) => (
          <TransactionTableItem key={transaction.id} transaction={transaction} />
        ))}
      </TableBody>
    </Table>
  );
}
