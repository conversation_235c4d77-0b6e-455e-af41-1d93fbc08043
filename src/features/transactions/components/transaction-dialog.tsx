import type { Transaction } from "../types";

import { useMemo } from "react";
import { useToggle } from "react-use";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { formatISO } from "date-fns";
import { toast } from "sonner";

import {
  getAccountsQueryKey,
  getBudgetsQueryKey,
  getSavingsGoalsQueryKey,
  getTransactionsInfiniteQueryKey,
  getTransactionsQueryKey,
  removeTransactionMutation,
} from "~/api/@tanstack/react-query.gen";
import { Button } from "~/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "~/components/ui/dialog";
import { useConfirmation } from "~/features/confirmations";

import { TransactionData } from "../types";
import TransactionForm from "./transaction-form";

interface Props {
  transaction?: Transaction;
  children: React.ReactNode;
  defaultValues?: TransactionData;
}

export default function TransactionDialog({ transaction, children: trigger, defaultValues: propDefaultValues }: Props) {
  const queryClient = useQueryClient();
  const ask = useConfirmation();

  const invalidateQueries = () => {
    void queryClient.invalidateQueries({ queryKey: getTransactionsQueryKey({ query: { page: 1, per_page: 5 } }) });
    void queryClient.invalidateQueries({ queryKey: getTransactionsInfiniteQueryKey() });
    void queryClient.invalidateQueries({ queryKey: getAccountsQueryKey() });
    void queryClient.invalidateQueries({ queryKey: getBudgetsQueryKey() });
    void queryClient.invalidateQueries({ queryKey: getSavingsGoalsQueryKey() });
  };

  const { mutate: removeTransaction } = useMutation({
    ...removeTransactionMutation(),
    onSuccess: () => {
      invalidateQueries();
      toast("Transaction deleted successfully!");
    },
  });

  const [open, toggleOpen] = useToggle(false);

  const isCreate = transaction === undefined;

  const defaultValues = useMemo<TransactionData>(() => {
    // If defaultValues prop is provided, use it
    if (propDefaultValues) {
      return propDefaultValues;
    }

    // Otherwise, use transaction data or defaults
    return {
      account_id: transaction?.account_id ?? "",
      account_to_id: transaction?.account_to_id ?? null,
      amount: transaction?.amount ?? "0",
      amount_to: transaction?.amount_to ?? "0",
      category_id: transaction?.category_id ?? null,
      description: transaction?.description ?? "",
      transaction_date: transaction?.transaction_date ?? formatISO(new Date(), { representation: "date" }),
      transaction_type: transaction?.transaction_type ?? "expense",
    };
  }, [transaction, propDefaultValues]);

  const handleDelete = async () => {
    if (!transaction) return;

    const ok = await ask({
      title: "Delete Transaction",
      description: "Are you sure you want to delete this transaction? This action cannot be undone.",
      confirmText: "Yes, delete",
      confirmVariant: "destructive",
    });
    if (!ok) return;

    removeTransaction({ path: { transaction_id: transaction.id } });
  };

  const onSuccess = () => {
    invalidateQueries();
    toggleOpen();
  };

  return (
    <Dialog open={open} onOpenChange={toggleOpen}>
      <DialogTrigger asChild>{trigger}</DialogTrigger>
      <DialogContent className="sm:max-w-[600px]">
        <DialogHeader>
          <DialogTitle>{isCreate ? "Create transaction" : `Edit transaction`}</DialogTitle>
          <DialogDescription>{isCreate ? "Create a new transaction." : "Update transaction details"}</DialogDescription>
        </DialogHeader>

        <div className="pb-4">
          <TransactionForm
            formId="transaction-form"
            transaction={transaction}
            onSuccess={onSuccess}
            defaultValues={defaultValues}
          />
        </div>

        <DialogFooter>
          {transaction && (
            <Button type="button" variant="secondary" className="me-auto" onClick={handleDelete}>
              Delete
            </Button>
          )}

          <DialogClose asChild>
            <Button variant="ghost">Cancel</Button>
          </DialogClose>
          <Button type="submit" form="transaction-form">
            {isCreate ? "Create transaction" : "Save changes"}
          </Button>
        </DialogFooter>
      </DialogContent>
    </Dialog>
  );
}
