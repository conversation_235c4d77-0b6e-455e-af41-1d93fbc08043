import type { Account, Category, Transaction } from "~/api";

import { formatISO } from "date-fns";
import { Copy as CopyIcon, SquareChartGantt as ViewIcon } from "lucide-react";

import { Button } from "~/components/ui/button";
import { TableCell, TableRow } from "~/components/ui/table";
import { useCurrentUser } from "~/features/auth/hooks";
import { formatCurrency } from "~/features/currencies/formatters";
import { formatDate } from "~/lib/formatters";
import { cn } from "~/lib/utils";

import TransactionDialog from "./transaction-dialog";
import TransactionIcon from "./transaction-icon";

interface Props {
  transaction: Transaction;
}

export default function TransactionTableItem({ transaction }: Props) {
  const currentUser = useCurrentUser();

  const isTransfer = transaction.transaction_type === "transfer";
  const isExpense = transaction.transaction_type === "expense";
  const isIncome = transaction.transaction_type === "income";

  const renderTransactionInfo = () => {
    return (
      <div className="flex flex-row items-center gap-4">
        <TransactionIcon transaction={transaction} />
        <div>
          <p className="text-dark text-base font-medium">
            {isTransfer
              ? "Transfer between accounts"
              : ((transaction.category as Category | null)?.name ?? "Uncategorized")}
          </p>
          {transaction.description && <p className="text-gray-03 text-xs">{transaction.description}</p>}
        </div>
      </div>
    );
  };

  const renderAccountInfo = () => {
    if (isTransfer) {
      const accountTo = transaction.account_to as Account;
      return (
        <div>
          <p className="text-dark-secondary text-base font-medium">{transaction.account.name}</p>
          <p className="text-gray-03 text-xs leading-4">→ {accountTo.name}</p>
        </div>
      );
    }

    return <p className="text-dark-secondary text-base font-medium">{transaction.account.name}</p>;
  };

  const renderAmountInfo = () => {
    if (isTransfer) {
      const accountTo = transaction.account_to as Account;
      return (
        <div>
          <p className="text-dark-secondary text-base font-medium">
            {formatCurrency(transaction.account.currency, transaction.amount)}
          </p>
          <p className="text-gray-03 text-xs leading-4">{formatCurrency(accountTo.currency, transaction.amount_to)}</p>
        </div>
      );
    }

    return (
      <div>
        <p
          className={cn("text-base font-medium", {
            "text-emerald-700": isIncome,
            "text-rose-700": isExpense,
          })}
        >
          {formatCurrency(transaction.account.currency, transaction.amount)}
        </p>
        {transaction.account.currency !== currentUser.base_currency && (
          <p className="text-gray-03 text-xs leading-4">
            {formatCurrency(currentUser.base_currency, transaction.base_amount)}
          </p>
        )}
      </div>
    );
  };

  // Create a copy of the transaction with current date
  const getCopyTransactionData = () => {
    return {
      account_id: transaction.account_id,
      account_to_id: transaction.account_to_id,
      amount: transaction.amount,
      amount_to: transaction.amount_to,
      category_id: transaction.category_id,
      description: transaction.description,
      transaction_date: formatISO(new Date(), { representation: "date" }),
      transaction_type: transaction.transaction_type,
    };
  };

  return (
    <TableRow>
      <TableCell className="py-6">{renderTransactionInfo()}</TableCell>
      <TableCell className="text-dark-secondary py-6 text-base font-medium">
        {formatDate(transaction.transaction_date)}
      </TableCell>
      <TableCell className="py-6">{renderAccountInfo()}</TableCell>
      <TableCell className="py-6 text-right">{renderAmountInfo()}</TableCell>
      <TableCell className="flex justify-end gap-1 py-6 text-right">
        <TransactionDialog defaultValues={getCopyTransactionData()}>
          <Button variant="ghost" size="icon" title="Copy transaction">
            <CopyIcon className="size-5 text-gray-600" />
            <span className="hidden">Copy</span>
          </Button>
        </TransactionDialog>

        <TransactionDialog transaction={transaction}>
          <Button variant="ghost" size="icon" title="View/edit transaction">
            <ViewIcon className="size-5 text-gray-600" />
          </Button>
        </TransactionDialog>
      </TableCell>
    </TableRow>
  );
}
