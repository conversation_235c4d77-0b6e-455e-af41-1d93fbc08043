import { Category, Transaction } from "~/api";
import { formatCurrency } from "~/features/currencies/formatters";
import { useRecentTransactions } from "~/features/transactions/hooks";
import { formatDate } from "~/lib/formatters";
import { cn } from "~/lib/utils";

import TransactionIcon from "./transaction-icon";

interface RecentTransactionsWidgetProps {
  limit?: number;
}

export default function RecentTransactionsWidget({ limit = 5 }: RecentTransactionsWidgetProps) {
  const { transactions, isLoading } = useRecentTransactions(limit);

  if (isLoading) {
    return (
      <div className="flex min-h-52 items-center justify-center">
        <div className="border-primary h-8 w-8 animate-spin rounded-full border-4 border-t-transparent"></div>
      </div>
    );
  }

  if (!transactions.length) {
    return (
      <div className="flex h-full min-h-52 flex-col items-center justify-center gap-4">
        <p className="text-center text-sm text-gray-600">No recent transactions to show.</p>
      </div>
    );
  }

  return (
    <div className="-my-4 flex flex-col divide-y divide-gray-100">
      {transactions.map((transaction) => (
        <TransactionItem transaction={transaction} key={transaction.id} />
      ))}
    </div>
  );
}

function TransactionItem({ transaction }: { transaction: Transaction }) {
  const isTransfer = transaction.transaction_type === "transfer";

  return (
    <div key={transaction.id} className="flex flex-row items-center gap-4 py-6">
      <TransactionIcon transaction={transaction} />
      <div className="flex-grow">
        <p className="text-dark text-base font-medium">
          {isTransfer
            ? "Transfer between accounts"
            : ((transaction.category as Category | null)?.name ?? "Uncategorized")}
        </p>
        {transaction.description && <p className="text-gray-03 text-xs leading-4">{transaction.description}</p>}
      </div>
      <div className="text-right">
        <p
          className={cn("text-base font-medium", {
            "text-emerald-700": transaction.transaction_type === "income",
            "text-rose-700": transaction.transaction_type === "expense",
          })}
        >
          {formatCurrency(transaction.account.currency, transaction.amount)}
        </p>
        <p className="text-gray-03 text-xs leading-4">{formatDate(transaction.transaction_date)}</p>
      </div>
    </div>
  );
}
