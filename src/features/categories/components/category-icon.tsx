import { BookMarkedIcon as DefaultIcon } from "lucide-react";

import { Category } from "~/api";
import { cn } from "~/lib/utils";

type Props = React.ComponentProps<"svg"> & { category: Category | null };

export default function CategoryIcon({ className, category, ...props }: Props) {
  if (!category) {
    return <DefaultIcon className={cn("inline-block size-6", className)} {...props} />;
  }

  return <DefaultIcon className={cn("inline-block size-6", className)} {...props} style={{ color: category.color }} />;
}
