import type { Category } from "../types";

import { useToggle } from "react-use";

import { Button } from "~/components/ui/button";
import {
  Dialog,
  DialogClose,
  DialogContent,
  DialogDescription,
  DialogFooter,
  DialogHeader,
  DialogTitle,
  DialogTrigger,
} from "~/components/ui/dialog";

import CategoryCreateForm from "./category-create-form";
import CategoryUpdateForm from "./category-update-form";

interface Props {
  category?: Category;
  children: React.ReactNode;
  defaultIsExpense?: boolean;
}

export default function CategoryDialog({ category, children: trigger, defaultIsExpense }: Props) {
  const [open, toggleOpen] = useToggle(false);

  const isCreate = category === undefined;
  const isUpdate = category !== undefined;

  return (
    <Dialog open={open} onOpenChange={toggleOpen}>
      <DialogTrigger asChild>{trigger}</DialogTrigger>
      <DialogContent>
        <DialogHeader>
          <DialogTitle>{isCreate ? "Create category" : `Edit ${category.name}`}</DialogTitle>
          <DialogDescription>
            {isCreate ? "Create a new category to organize your transactions." : "Update category details"}
          </DialogDescription>

          <div className="py-4">
            {isCreate && <CategoryCreateForm formId="category-form" onSuccess={toggleOpen} defaultIsExpense={defaultIsExpense} />}
            {isUpdate && <CategoryUpdateForm formId="category-form" category={category} onSuccess={toggleOpen} />}
          </div>

          <DialogFooter>
            <DialogClose asChild>
              <Button variant="ghost">Cancel</Button>
            </DialogClose>
            <Button type="submit" form="category-form">
              {isCreate ? "Create category" : "Save changes"}
            </Button>
          </DialogFooter>
        </DialogHeader>
      </DialogContent>
    </Dialog>
  );
}
