import { PlusIcon } from "lucide-react";

import ContentBlock from "~/components/blocks/content-block";
import { Button } from "~/components/ui/button";
import CategoryListItem from "~/features/categories/components/category-list-item";

import { Category } from "../types";
import CategoryDialog from "./category-dialog";

interface Props {
  title: string;
  categories: Category[];
  isExpense?: boolean;
}

export default function CategoryList({ title, categories, isExpense }: Props) {
  return (
    <ContentBlock title={title}>
      {!categories.length && <p className="px-4 py-4 text-sm text-gray-600 italic">No categories to show.</p>}

      {categories.length > 0 && (
        <div className="flex flex-col gap-2">
          {categories.map((category) => (
            <CategoryListItem category={category} key={category.id} />
          ))}
        </div>
      )}

      <div className="mt-2 border-t border-gray-100 pt-2 text-center">
        <CategoryDialog defaultIsExpense={!!isExpense}>
          <Button variant="ghost" size="sm">
            <PlusIcon className="mr-2 h-4 w-4" /> {isExpense ? "Add expense category" : "Add income category"}
          </Button>
        </CategoryDialog>
      </div>
    </ContentBlock>
  );
}
