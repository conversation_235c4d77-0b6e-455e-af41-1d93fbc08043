import type { Category, UpdateCategoryData } from "../types";

import { useForm } from "react-hook-form";

import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import { getCategoriesQueryKey, updateCategoryMutation } from "~/api/@tanstack/react-query.gen";
import InputBoolean from "~/components/inputs/input-boolean";
import InputText from "~/components/inputs/input-text";
import { Form } from "~/components/ui/form";

import { UpdateCategorySchema } from "../schemas";

interface Props {
  formId: string;
  category: Category;
  onSuccess?: () => void;
}

export default function CategoryUpdateForm({ formId, category, onSuccess }: Props) {
  const queryClient = useQueryClient();

  const { mutate: updateCategory } = useMutation({
    ...updateCategoryMutation(),
    onSuccess() {
      void queryClient.invalidateQueries({ queryKey: getCategoriesQueryKey() });
      toast("Category updated successfully.");
      onSuccess?.();
    },
  });

  const form = useForm({
    resolver: zodResolver<UpdateCategoryData>(UpdateCategorySchema),
    defaultValues: {
      name: category.name,
      is_expense: category.is_expense,
      color: category.color,
      icon: category.icon,
    },
  });

  const onSubmit = form.handleSubmit((data) => {
    updateCategory({ body: data, path: { category_id: category.id } });
  });

  return (
    <Form {...form}>
      <form id={formId} onSubmit={onSubmit} className="grid grid-cols-1 gap-4 md:grid-cols-4">
        <InputText control={form.control} name="name" label="Name" className="md:col-span-3" />
        <InputText control={form.control} name="color" label="Color" type="color" />
        <div className="md:col-span-4">
          <InputBoolean
            control={form.control}
            name="is_expense"
            label="Is expense?"
            description="Check if this category is for expenses, uncheck for income"
          />
        </div>
      </form>
    </Form>
  );
}