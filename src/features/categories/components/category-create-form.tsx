import type { CreateCategoryData } from "../types";

import { useForm } from "react-hook-form";

import { zodResolver } from "@hookform/resolvers/zod";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import { createCategoryMutation, getCategoriesQueryKey } from "~/api/@tanstack/react-query.gen";
import InputBoolean from "~/components/inputs/input-boolean";
import InputText from "~/components/inputs/input-text";
import { Form } from "~/components/ui/form";

import { CreateCategorySchema } from "../schemas";

interface Props {
  formId: string;
  onSuccess?: () => void;
  defaultIsExpense?: boolean;
}

const defaultColor = "#129692";

export default function CategoryCreateForm({ formId, onSuccess, defaultIsExpense = true }: Props) {
  const queryClient = useQueryClient();

  const { mutate: createCategory } = useMutation({
    ...createCategoryMutation(),
    onSuccess: () => {
      void queryClient.invalidateQueries({ queryKey: getCategoriesQueryKey() });
      toast("Category created successfully!");
      onSuccess?.();
    },
  });

  const form = useForm({
    resolver: zodResolver<CreateCategoryData>(CreateCategorySchema),
    defaultValues: {
      name: "",
      is_expense: defaultIsExpense,
      color: defaultColor,
      icon: null,
    },
  });

  const onSubmit = form.handleSubmit((data) => {
    createCategory({ body: data });
  });

  return (
    <Form {...form}>
      <form id={formId} onSubmit={onSubmit} className="grid grid-cols-1 gap-4 md:grid-cols-4">
        <InputText control={form.control} name="name" label="Name" className="md:col-span-3" />
        <InputText control={form.control} name="color" label="Color" type="color" />
        <div className="md:col-span-4">
          <InputBoolean
            control={form.control}
            name="is_expense"
            label="Is expense?"
            description="Check if this category is for expenses, uncheck for income"
          />
        </div>
      </form>
    </Form>
  );
}
