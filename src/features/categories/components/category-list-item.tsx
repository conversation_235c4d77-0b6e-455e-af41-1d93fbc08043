import type { Category } from "../types";

import { useMutation, useQueryClient } from "@tanstack/react-query";
import { toast } from "sonner";

import { getCategoriesQueryKey, removeCategoryMutation } from "~/api/@tanstack/react-query.gen";
import { Button } from "~/components/ui/button";
import { useConfirmation } from "~/features/confirmations";

import CategoryDialog from "./category-dialog";
import CategoryIcon from "./category-icon";

interface Props {
  category: Category;
}

export default function CategoryListItem({ category }: Props) {
  const confirm = useConfirmation();
  const queryClient = useQueryClient();

  const { mutate: removeCategory } = useMutation({
    ...removeCategoryMutation(),
    onSuccess() {
      void queryClient.invalidateQueries({ queryKey: getCategoriesQueryKey() });
      toast("Category has been removed!");
    },
    onError() {
      toast("Error while removing category");
    },
  });

  const handleRemove = async () => {
    const ok = await confirm({
      title: "Remove Category",
      description: `Are you sure you want to remove "${category.name}"? This action cannot be undone.`,
      confirmText: "Remove",
      confirmVariant: "destructive",
    });

    if (ok) {
      removeCategory({ path: { category_id: category.id } });
    }
  };

  return (
    <div className="flex flex-row items-center justify-between gap-2">
      <p>
        <span className="inline-block rounded-md bg-gray-100 p-2">
          <CategoryIcon category={category} />
        </span>
        <span className="ps-2">{category.name}</span>
      </p>

      <div className="flex items-center gap-1">
        <CategoryDialog category={category}>
          <Button variant="link" size="sm">
            Edit
          </Button>
        </CategoryDialog>
        <Button variant="link" size="sm" onClick={handleRemove}>
          Remove
        </Button>
      </div>
    </div>
  );
}
