import { useMemo } from "react";

import { useQuery } from "@tanstack/react-query";

import { getCategoriesOptions } from "~/api/@tanstack/react-query.gen";

export function useCategories() {
  const { data, isLoading, isError, error } = useQuery(getCategoriesOptions());

  const categories = useMemo(() => data ?? [], [data]);

  const expenseCategories = useMemo(() => {
    return categories.filter((category) => category.is_expense);
  }, [categories]);

  const incomeCategories = useMemo(() => {
    return categories.filter((category) => !category.is_expense);
  }, [categories]);

  return { isLoading, isError, error, categories, expenseCategories, incomeCategories };
}
