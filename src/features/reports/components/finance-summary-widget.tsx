import { MinusIcon, TrendingDownIcon, TrendingUpIcon } from "lucide-react";

import { AlertDestructive, ContentBlock } from "~/components/blocks";
import { BlockLoader } from "~/components/elements";
import { useCurrentUser } from "~/features/auth/hooks";
import { formatCurrency } from "~/features/currencies/formatters";
import { useFinancialSummary } from "~/features/reports/hooks";
import { calculatePercentageChange } from "~/lib/finances";
import { formatPercent } from "~/lib/formatters";
import { cn } from "~/lib/utils";

function ValueRow({
  label,
  currency,
  value,
  prevValue,
  reverse = false,
}: {
  label: string;
  currency: string;
  value: string;
  prevValue: string;
  reverse?: boolean;
}) {
  const change = calculatePercentageChange(value, prevValue);

  return (
    <div className="flex flex-row gap-2 pt-5">
      <p>
        <span className="inline-block rounded-md bg-gray-100 p-1">
          {change > 0 && (
            <TrendingUpIcon className={cn("size-4", { "text-emerald-400": !reverse, "text-rose-400": reverse })} />
          )}
          {change < 0 && (
            <TrendingDownIcon className={cn("size-4", { "text-emerald-400": reverse, "text-rose-400": !reverse })} />
          )}
          {change === 0 && <MinusIcon className="size-4 text-gray-400" />}
        </span>
      </p>
      <div>
        <p className="text-gray-02 text-xs">{label}</p>
        <p className="text-dark text-base font-bold">{formatCurrency(currency, value)}</p>
      </div>
      <div className="ms-auto flex items-end">
        <p
          className={cn("text-xs", {
            "text-emerald-400": (change > 0 && !reverse) || (change < 0 && reverse),
            "text-rose-400": (change < 0 && !reverse) || (change > 0 && reverse),
            "text-gray-02": change === 0,
          })}
        >
          {formatPercent(change)}
        </p>
      </div>
    </div>
  );
}

export default function FinanceSummaryWidget() {
  const currentUser = useCurrentUser();

  const { summary, isLoading, isError } = useFinancialSummary();

  if (isLoading) {
    return (
      <ContentBlock title="Balance overview">
        <BlockLoader className="min-h-24" />
      </ContentBlock>
    );
  }

  if (isError || !summary) {
    return (
      <div>
        <AlertDestructive title="Error" message="Error loading financial summary" />
      </div>
    );
  }

  return (
    <ContentBlock title="Total Balance">
      <div className="flex flex-row items-center justify-between gap-2 border-b border-gray-100 pb-3">
        <p className="text-dark block text-xl font-bold">
          {formatCurrency(currentUser.base_currency, summary.total_balance)}
        </p>
        <p className="text-dark-secondary block pt-0.5 text-sm font-medium">All accounts</p>
      </div>

      <ValueRow
        label="This month income"
        currency={currentUser.base_currency}
        value={summary.current_month.income}
        prevValue={summary.previous_month.income}
      />

      <ValueRow
        label="This month expenses"
        currency={currentUser.base_currency}
        value={summary.current_month.expenses}
        prevValue={summary.previous_month.expenses}
        reverse
      />
    </ContentBlock>
  );
}
