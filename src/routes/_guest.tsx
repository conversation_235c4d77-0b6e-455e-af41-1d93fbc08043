import { createFileRoute, Outlet, redirect } from "@tanstack/react-router";
import { z } from "zod";

import Logo from "~/components/elements/logo";

export const Route = createFileRoute("/_guest")({
  component: RouteComponent,
  validateSearch: z.object({ next: z.string().optional() }),
  beforeLoad: ({ context, search }) => {
    if (context.isAuthenticated) {
      throw redirect({ to: search.next ?? "/" });
    }
  },
});

function RouteComponent() {
  return (
    <div className="flex h-full flex-col items-center justify-center py-6 sm:py-12">
      <main className="relative w-full py-3 sm:mx-auto sm:w-auto sm:max-w-xl">
        <div className="bg-dark absolute inset-0 -skew-y-6 transform shadow-xs sm:-rotate-6 sm:skew-y-0 sm:rounded-xl"></div>

        <div className="relative bg-white px-4 py-10 shadow-xs sm:rounded-xl sm:p-20">
          <div className="mx-auto max-w-md">
            <div>
              <a href="#" className="flex flex-col items-center justify-center gap-2">
                <Logo className="text-primary h-10" />
                <span className="text-dark text-4xl font-extrabold">Finanze.Pro</span>
              </a>
            </div>

            <div className="pt-8">
              <Outlet />
            </div>
          </div>
        </div>
      </main>
    </div>
  );
}
