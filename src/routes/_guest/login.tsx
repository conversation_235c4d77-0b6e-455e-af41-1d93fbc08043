import { createFileRoute, <PERSON> } from "@tanstack/react-router";

import LoginForm from "~/components/forms/login-form";

export const Route = createFileRoute("/_guest/login")({
  component: RouteComponent,
});

function RouteComponent() {
  return (
    <>
      <h1 className="text-xl font-semibold text-gray-800 sm:text-2xl">Login</h1>
      <p className="pt-2 text-sm text-gray-600">Sign in for your account</p>

      <div className="pt-4">
        <LoginForm />
      </div>

      <div className="pt-2">
        <p className="text-sm text-gray-500">
          Don&apos;t have an account?{" "}
          <Link to="/signup" className="text-primary font-medium hover:underline">
            Sign up
          </Link>
        </p>
      </div>
    </>
  );
}
