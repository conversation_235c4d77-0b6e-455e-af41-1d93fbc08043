import { createFile<PERSON>out<PERSON>, <PERSON> } from "@tanstack/react-router";

import PasswordResetRequestForm from "~/components/forms/password-reset-request-form";

export const Route = createFileRoute("/_guest/request-reset-password")({
  component: RouteComponent,
});

function RouteComponent() {
  return (
    <>
      <h1 className="text-xl font-semibold text-gray-800 sm:text-2xl">Reset Password</h1>
      <p className="pt-2 text-sm text-gray-600">Enter your email to receive a password reset link</p>

      <div className="pt-4">
        <PasswordResetRequestForm />
      </div>

      <div className="pt-2">
        <p className="text-sm text-gray-500">
          Remember your password?{" "}
          <Link to="/login" className="text-primary font-medium hover:underline">
            Login
          </Link>
        </p>
      </div>
    </>
  );
}
