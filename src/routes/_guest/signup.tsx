import { createFileRoute, <PERSON> } from "@tanstack/react-router";

import SignupForm from "~/components/forms/signup-form";

export const Route = createFileRoute("/_guest/signup")({
  component: RouteComponent,
});

function RouteComponent() {
  return (
    <>
      <h1 className="text-xl font-semibold text-gray-800 sm:text-2xl">Signup</h1>
      <p className="pt-2 text-sm text-gray-600">Register a new account</p>

      <div className="pt-4">
        <SignupForm />
      </div>

      <div className="pt-2">
        <p className="text-sm text-gray-500">
          Already have an account?{" "}
          <Link to="/login" className="text-primary font-medium hover:underline">
            Login
          </Link>
        </p>
      </div>
    </>
  );
}
