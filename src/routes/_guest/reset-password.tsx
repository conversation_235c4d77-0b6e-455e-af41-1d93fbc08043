import { create<PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON> } from "@tanstack/react-router";
import { z } from "zod";

import PasswordResetForm from "~/components/forms/password-reset-form";

export const Route = createFileRoute("/_guest/reset-password")({
  component: RouteComponent,
  validateSearch: z.object({
    token: z.string().optional(),
  }),
});

function RouteComponent() {
  const { token } = Route.useSearch();

  if (!token) {
    return (
      <>
        <h1 className="text-xl font-semibold text-gray-800 sm:text-2xl">Invalid Reset Link</h1>
        <p className="pt-2 text-sm text-gray-600">
          The password reset link is invalid or has expired. Please request a new one.
        </p>

        <div className="pt-4">
          <Link
            to="/request-reset-password"
            className="bg-primary hover:bg-primary/90 focus-visible:ring-ring inline-flex items-center justify-center rounded-md px-4 py-2 text-sm font-medium text-white shadow transition-colors focus-visible:ring-1 focus-visible:outline-none disabled:pointer-events-none disabled:opacity-50"
          >
            Request New Link
          </Link>
        </div>
      </>
    );
  }

  return (
    <>
      <h1 className="text-xl font-semibold text-gray-800 sm:text-2xl">Reset Password</h1>
      <p className="pt-2 text-sm text-gray-600">Enter your new password</p>

      <div className="pt-4">
        <PasswordResetForm token={token as string} />
      </div>

      <div className="pt-2">
        <p className="text-sm text-gray-500">
          Remember your password?{" "}
          <Link to="/login" className="text-primary font-medium hover:underline">
            Login
          </Link>
        </p>
      </div>
    </>
  );
}
