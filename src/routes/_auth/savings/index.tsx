import { useMemo, useState } from "react";

import { createFileRoute } from "@tanstack/react-router";
import { PlusIcon } from "lucide-react";

import { getSavingsGoalsOptions } from "~/api/@tanstack/react-query.gen";
import ContentSection from "~/components/blocks/content-section";
import BlockLoader from "~/components/elements/block-loader";
import { Button } from "~/components/ui/button";
import { Card, CardContent } from "~/components/ui/card";
import { useCurrentUser } from "~/features/auth/hooks";
import { formatCurrency } from "~/features/currencies/formatters";
import {
  SavingsGoalCard,
  SavingsGoalDialog,
  SavingsGoalFilter,
  useSavingsGoals,
  SavingsGoalStatus,
} from "~/features/savings";

export const Route = createFileRoute("/_auth/savings/")({
  loader: ({ context }) => context.queryClient.ensureQueryData(getSavingsGoalsOptions()),
  pendingComponent: BlockLoader,
  component: RouteComponent,
});

function RouteComponent() {
  const currentUser = useCurrentUser();
  const { activeGoals, completedGoals, archivedGoals, getFilteredGoals, getTotalAmount, isLoading } = useSavingsGoals();
  const [statusFilter, setStatusFilter] = useState<SavingsGoalStatus>("active");

  const filteredGoals = useMemo(() => {
    return getFilteredGoals(statusFilter);
  }, [getFilteredGoals, statusFilter]);

  const totalAmount = useMemo(() => {
    return getTotalAmount(filteredGoals);
  }, [getTotalAmount, filteredGoals]);

  const handleFilterChange = (status: SavingsGoalStatus) => {
    setStatusFilter(status);
  };

  if (isLoading) {
    return <BlockLoader />;
  }

  return (
    <ContentSection
      title="Savings Goals"
      action={
        <div className="flex items-center gap-4">
          <div className="hidden md:block">
            <p className="text-sm font-medium">
              Total: <span className="font-bold">{formatCurrency(currentUser.base_currency, totalAmount.toString())}</span>
            </p>
          </div>
          <SavingsGoalFilter
            activeCount={activeGoals.length}
            completedCount={completedGoals.length}
            archivedCount={archivedGoals.length}
            onChange={handleFilterChange}
          />
          {statusFilter === "active" && (
            <SavingsGoalDialog>
              <Button variant="outline" size="sm">
                <PlusIcon className="mr-1 h-4 w-4" /> Add Goal
              </Button>
            </SavingsGoalDialog>
          )}
        </div>
      }
    >
      <div className="md:hidden mb-4">
        <p className="text-sm font-medium">
          Total: <span className="font-bold">{formatCurrency(currentUser.base_currency, totalAmount.toString())}</span>
        </p>
      </div>
      
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
        {!filteredGoals.length && (
          <p className="col-span-full pt-4 text-sm text-gray-600 italic">
            {statusFilter === "active"
              ? "No active savings goals. Create one to start tracking your savings!"
              : statusFilter === "completed"
              ? "No completed savings goals."
              : "No archived savings goals."}
          </p>
        )}

        {filteredGoals.map((goal) => (
          <SavingsGoalCard key={goal.id} goal={goal} />
        ))}

        {statusFilter === "active" && (
          <Card>
            <CardContent className="flex h-full min-h-24 items-center justify-center">
              <SavingsGoalDialog>
                <Button variant="ghost">
                  <PlusIcon /> Add goal
                </Button>
              </SavingsGoalDialog>
            </CardContent>
          </Card>
        )}
      </div>
    </ContentSection>
  );
}
