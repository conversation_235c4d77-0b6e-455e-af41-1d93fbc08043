import { createFileRoute, <PERSON> } from "@tanstack/react-router";
import { ChevronRightIcon } from "lucide-react";

import { ContentBlock } from "~/components/blocks";
import { BudgetDashboard } from "~/features/budgets/components";
import { FinanceSummaryWidget } from "~/features/reports/components";
import { SavingsGoalsWidget } from "~/features/savings";
import { RecentTransactionsWidget } from "~/features/transactions/components";

export const Route = createFileRoute("/_auth/")({
  component: Index,
});

function Index() {
  return (
    <div className="grid grid-cols-1 gap-x-6 gap-y-8 lg:grid-cols-3">
      <FinanceSummaryWidget />

      <BudgetDashboard />

      <SavingsGoalsWidget />

      <ContentBlock
        title="Recent transactions"
        className="lg:col-span-2 lg:row-span-2"
        action={
          <Link to="/transactions" className="link text-xs">
            <span>View all</span> <ChevronRightIcon className="size-4" />
          </Link>
        }
      >
        <RecentTransactionsWidget limit={5} />
      </ContentBlock>

      <ContentBlock title="Upcoming payments" className="lg:row-span-2">
        <div className="flex h-full min-h-24 items-center justify-center rounded-lg border border-dashed border-gray-300">
          <p className="text-sm text-gray-500">Coming soon...</p>
        </div>
      </ContentBlock>

      <ContentBlock title="Stats" className="lg:col-span-3">
        <div className="flex h-full min-h-52 items-center justify-center rounded-lg border border-dashed border-gray-300">
          <p className="text-sm text-gray-500">Coming soon...</p>
        </div>
      </ContentBlock>
    </div>
  );
}
