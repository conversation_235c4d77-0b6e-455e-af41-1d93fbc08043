import { useMemo } from "react";
import { useToggle } from "react-use";

import { createFileRoute } from "@tanstack/react-router";
import { PlusIcon } from "lucide-react";

import { getBudgetsOptions } from "~/api/@tanstack/react-query.gen";
import ContentSection from "~/components/blocks/content-section";
import BlockLoader from "~/components/elements/block-loader";
import { Button } from "~/components/ui/button";
import { Card, CardContent } from "~/components/ui/card";
import { BudgetCard, BudgetDialog, useBudgets } from "~/features/budgets";

export const Route = createFileRoute("/_auth/budgets/")({
  loader: ({ context }) => context.queryClient.ensureQueryData(getBudgetsOptions()),
  pendingComponent: BlockLoader,
  component: RouteComponent,
});

function RouteComponent() {
  const { activeBudgets, inactiveBudgets } = useBudgets();

  const [showInactive, toggleInactive] = useToggle(false);

  const filteredBudgets = useMemo(() => {
    return showInactive ? inactiveBudgets : activeBudgets;
  }, [activeBudgets, inactiveBudgets, showInactive]);

  const toggleAction =
    inactiveBudgets.length > 0 ? (
      <Button variant="outline" size="sm" onClick={toggleInactive}>
        {showInactive ? "Active budgets" : `Inactive (${inactiveBudgets.length.toString()})`}
      </Button>
    ) : null;

  return (
    <ContentSection title="Budgets" action={toggleAction}>
      <div className="grid grid-cols-1 gap-4">
        {!filteredBudgets.length && <p className="pt-4 text-sm text-gray-600 italic">No budgets to show.</p>}

        {filteredBudgets.map((budget) => (
          <BudgetCard key={budget.id} budget={budget} />
        ))}

        {!showInactive && (
          <Card>
            <CardContent className="flex h-full min-h-24 items-center justify-center">
              <BudgetDialog>
                <Button variant="ghost">
                  <PlusIcon /> Add budget
                </Button>
              </BudgetDialog>
            </CardContent>
          </Card>
        )}
      </div>
    </ContentSection>
  );
}
