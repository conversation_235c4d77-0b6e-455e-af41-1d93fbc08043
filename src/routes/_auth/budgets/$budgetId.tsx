import { createFileRoute } from "@tanstack/react-router";

import { getBudgetOptions } from "~/api/@tanstack/react-query.gen";
import ContentSection from "~/components/blocks/content-section";
import BlockLoader from "~/components/elements/block-loader";
import { BudgetDetails, BudgetPeriodDetails, BudgetPeriodsTable } from "~/features/budgets";

export const Route = createFileRoute("/_auth/budgets/$budgetId")({
  loader: ({ context, params }) =>
    context.queryClient.ensureQueryData(getBudgetOptions({ path: { budget_id: params.budgetId } })),
  pendingComponent: BlockLoader,
  component: RouteComponent,
});

function RouteComponent() {
  const { budgetId } = Route.useParams();

  return (
    <div className="space-y-8">
      <ContentSection title="Budget Details" goBack>
        <BudgetDetails budgetId={budgetId} />
      </ContentSection>

      <ContentSection title="Current Period">
        <BudgetPeriodDetails budgetId={budgetId} />
      </ContentSection>

      <ContentSection title="Budget Periods History">
        <BudgetPeriodsTable budgetId={budgetId} />
      </ContentSection>
    </div>
  );
}
