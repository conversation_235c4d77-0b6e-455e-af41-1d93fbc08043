import { useState } from "react";

import { createFileRoute } from "@tanstack/react-router";
import { PlusIcon } from "lucide-react";

import { getTransactionsInfiniteOptions } from "~/api/@tanstack/react-query.gen";
import ContentSection from "~/components/blocks/content-section";
import BlockLoader from "~/components/elements/block-loader";
import { Button } from "~/components/ui/button";
import TransactionDialog from "~/features/transactions/components/transaction-dialog";
import TransactionFilters, {
  TransactionFilters as Filters,
} from "~/features/transactions/components/transaction-filters";
import TransactionTable from "~/features/transactions/components/transaction-table";
import { TransactionFilters as FiltersType, useInfiniteTransactions } from "~/features/transactions/hooks";

export const Route = createFileRoute("/_auth/transactions")({
  loader: ({ context }) => context.queryClient.ensureInfiniteQueryData(getTransactionsInfiniteOptions()),
  pendingComponent: BlockLoader,
  component: Transactions,
});

function Transactions() {
  const [filters, setFilters] = useState<FiltersType>();
  const { transactions, isLoading, fetchNextPage, hasNextPage, isFetchingNextPage } = useInfiniteTransactions(filters);

  const handleLoadMore = () => {
    void fetchNextPage();
  };

  const handleFiltersChange = (newFilters: Filters) => {
    setFilters(newFilters);
  };

  return (
    <ContentSection
      title="Transactions"
      action={
        <div className="flex items-center gap-2">
          <TransactionDialog>
            <Button variant="outline" size="sm">
              <PlusIcon className="h-4 w-4" /> Add Transaction
            </Button>
          </TransactionDialog>
          <TransactionFilters onChange={handleFiltersChange} />
        </div>
      }
    >
      <div className="grow rounded-xl bg-white px-6 py-5">
        {/* eslint-disable-next-line @typescript-eslint/no-unnecessary-condition */}
        {isLoading ? (
          <BlockLoader />
        ) : (
          <div className="space-y-4">
            <TransactionTable transactions={transactions} />

            {hasNextPage && (
              <div className="mt-4 flex justify-center">
                <Button
                  variant="outline"
                  onClick={handleLoadMore}
                  disabled={isFetchingNextPage}
                  className="min-w-[200px]"
                >
                  {isFetchingNextPage ? "Loading..." : "Load More"}
                </Button>
              </div>
            )}
          </div>
        )}
      </div>
    </ContentSection>
  );
}
