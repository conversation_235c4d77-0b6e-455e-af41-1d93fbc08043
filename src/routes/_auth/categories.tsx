import { createFileRoute } from "@tanstack/react-router";
import { PlusIcon } from "lucide-react";

import { getCategoriesOptions } from "~/api/@tanstack/react-query.gen";
import ContentSection from "~/components/blocks/content-section";
import BlockLoader from "~/components/elements/block-loader";
import { Button } from "~/components/ui/button";
import CategoryDialog from "~/features/categories/components/category-dialog";
import CategoryList from "~/features/categories/components/category-list";
import { useCategories } from "~/features/categories/hooks";

export const Route = createFileRoute("/_auth/categories")({
  loader: ({ context }) => context.queryClient.ensureQueryData(getCategoriesOptions()),
  pendingComponent: BlockLoader,
  component: RouteComponent,
});

function RouteComponent() {
  const { expenseCategories, incomeCategories } = useCategories();

  const hasExpenseCategories = expenseCategories.length > 0;
  const hasIncomeCategories = incomeCategories.length > 0;
  const hasNoCategories = !hasExpenseCategories && !hasIncomeCategories;

  if (hasNoCategories) {
    return (
      <ContentSection title="Categories">
        <div className="flex h-40 flex-col items-center justify-center gap-4">
          <p>You don&apos;t have any categories yet</p>
          <CategoryDialog defaultIsExpense>
            <Button variant="outline" size="sm">
              <PlusIcon className="mr-2 h-4 w-4" /> Add first category
            </Button>
          </CategoryDialog>
        </div>
      </ContentSection>
    );
  }

  return (
    <div className="grid grid-cols-1 gap-4 md:grid-cols-2">
      <CategoryList title="Expenses" categories={expenseCategories} isExpense />
      <CategoryList title="Income" categories={incomeCategories} />
    </div>
  );
}
