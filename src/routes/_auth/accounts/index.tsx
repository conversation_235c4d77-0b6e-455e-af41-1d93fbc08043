import { useMemo, useState } from "react";
import { useToggle } from "react-use";

import { createFileRoute } from "@tanstack/react-router";
import { PlusIcon } from "lucide-react";

import { getAccountsOptions } from "~/api/@tanstack/react-query.gen";
import ContentSection from "~/components/blocks/content-section";
import BlockLoader from "~/components/elements/block-loader";
import { Button } from "~/components/ui/button";
import { Card, CardContent } from "~/components/ui/card";
import AccountCard from "~/features/accounts/components/account-card";
import AccountDialog from "~/features/accounts/components/account-dialog";
import AccountGroupFilter from "~/features/accounts/components/account-group-filter";
import { useAccounts } from "~/features/accounts/hooks";

export const Route = createFileRoute("/_auth/accounts/")({
  loader: ({ context }) => context.queryClient.ensureQueryData(getAccountsOptions()),
  pendingComponent: BlockLoader,
  component: RouteComponent,
});

function RouteComponent() {
  const { activeAccounts, archivedAccounts, groups } = useAccounts();

  const [showArchived, toggleArchived] = useToggle(false);
  const [selectedGroup, setSelectedGroup] = useState<string | null>(null);

  const filteredAccounts = useMemo(() => {
    const baseAccounts = showArchived ? archivedAccounts : activeAccounts;
    return selectedGroup === null ? baseAccounts : baseAccounts.filter((account) => account.group === selectedGroup);
  }, [activeAccounts, archivedAccounts, showArchived, selectedGroup]);

  const toggleAction =
    archivedAccounts.length > 0 ? (
      <Button variant="outline" size="sm" onClick={toggleArchived}>
        {showArchived ? "Active account" : `Archived (${archivedAccounts.length.toString()})`}
      </Button>
    ) : null;

  const groupFilterAction =
    groups.length > 0 ? <AccountGroupFilter groups={groups} onChange={setSelectedGroup} /> : null;

  return (
    <ContentSection
      title="Accounts"
      action={
        <div className="flex items-center">
          {toggleAction}
          {groupFilterAction}
        </div>
      }
    >
      <div className="grid grid-cols-1 gap-4 md:grid-cols-2 lg:grid-cols-3">
        {!filteredAccounts.length && <p className="pt-4 text-sm text-gray-600 italic">No accounts to show.</p>}

        {filteredAccounts.map((account) => (
          <AccountCard key={account.id} account={account} />
        ))}

        {!showArchived && (
          <Card>
            <CardContent className="flex h-full min-h-24 items-center justify-center">
              <AccountDialog>
                <Button variant="ghost">
                  <PlusIcon /> Add account
                </Button>
              </AccountDialog>
            </CardContent>
          </Card>
        )}
      </div>
    </ContentSection>
  );
}
