import { createFileRoute } from "@tanstack/react-router";

import { getAccountOptions } from "~/api/@tanstack/react-query.gen";
import ContentSection from "~/components/blocks/content-section";
import BlockLoader from "~/components/elements/block-loader";
import AccountDetails from "~/features/accounts/components/account-details";
import AccountTransactions from "~/features/accounts/components/account-transactions";

export const Route = createFileRoute("/_auth/accounts/$accountId")({
  loader: ({ context, params }) =>
    context.queryClient.ensureQueryData(getAccountOptions({ path: { account_id: params.accountId } })),
  pendingComponent: BlockLoader,
  component: RouteComponent,
});

function RouteComponent() {
  const { accountId } = Route.useParams();

  return (
    <div className="space-y-8">
      <ContentSection title="Account Details" goBack>
        <AccountDetails accountId={accountId} />
      </ContentSection>

      <ContentSection title="Transactions History">
        <AccountTransactions accountId={accountId} />
      </ContentSection>
    </div>
  );
}
