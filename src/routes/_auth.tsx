import { createFileRoute, Outlet, redirect } from "@tanstack/react-router";

import BlockLoader from "~/components/elements/block-loader";
import Header from "~/components/layout/header";
import Sidebar from "~/components/layout/sidebar";

export const Route = createFileRoute("/_auth")({
  component: RouteComponent,
  pendingComponent: BlockLoader,
  beforeLoad: ({ context }) => {
    if (!context.isAuthenticated) {
      // eslint-disable-next-line @typescript-eslint/only-throw-error
      throw redirect({ to: "/login", search: { next: location.href } });
    }
  },
});

function RouteComponent() {
  return (
    <div className="mx-auto grid h-dvh max-w-[1440px] grid-cols-1 lg:grid-cols-[280px_1fr]">
      <div className="hidden md:block">
        <Sidebar />
      </div>

      <div className="bg-[#F4F5F7]">
        <Header />

        <main className="px-4 py-4 md:ps-6 md:pe-8">
          <Outlet />
        </main>
      </div>
    </div>
  );
}
