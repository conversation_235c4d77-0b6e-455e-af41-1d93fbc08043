# TypeScript/React Style Guide for Finance.Pro

This style guide outlines the coding conventions, architectural patterns, and best practices for the Finance.Pro web application. Following these guidelines ensures consistency, maintainability, and high code quality across the project.

## Table of Contents

1. [Project Architecture](#project-architecture)
2. [TypeScript Conventions](#typescript-conventions)
3. [React Patterns](#react-patterns)
4. [Component Structure](#component-structure)
5. [State Management](#state-management)
6. [Styling Approach](#styling-approach)
7. [Form Handling](#form-handling)
8. [API Integration](#api-integration)
9. [Common Pitfalls](#common-pitfalls)

## Project Architecture

### Feature-Based Organization

The project follows a feature-based architecture where code is organized by business domain rather than technical concerns:

```
src/
├── api/            # API client configuration and services
├── components/     # Shared UI components
│   ├── blocks/     # Composite components
│   ├── elements/   # Basic UI elements
│   ├── forms/      # Form-related components
│   ├── inputs/     # Input components
│   ├── layout/     # Layout components
│   └── ui/         # UI primitives (based on Radix UI)
├── features/       # Feature modules
│   ├── accounts/   # Account management feature
│   │   ├── components/  # Feature-specific components
│   │   ├── constants.ts # Feature-specific constants
│   │   ├── hooks.ts     # Feature-specific hooks
│   │   ├── schemas.ts   # Validation schemas
│   │   └── types.ts     # TypeScript types
│   └── ...
├── lib/            # Utility functions and helpers
└── routes/         # Application routes
```

### Key Principles

- **Separation of Concerns**: Each feature module should be self-contained with its own components, types, hooks, and utilities.
- **Domain-Driven Design**: Code organization should reflect the business domains rather than technical concerns.
- **Component Composition**: Prefer composition over inheritance for building UI components.

## TypeScript Conventions

### Type Safety

- Use strict TypeScript configuration with all strict checks enabled.
- Avoid using `any` type; prefer `unknown` when the type is truly unknown.
- Use explicit return types for functions, especially for exported functions and React components.

```typescript
// Good
function formatAmount(amount: number, currency: string): string {
  return `${amount.toFixed(2)} ${currency}`;
}

// Avoid
function formatAmount(amount, currency) {
  return `${amount.toFixed(2)} ${currency}`;
}
```

### Type Definitions

- Define interfaces and types in dedicated `.ts` files (e.g., `types.ts`) within feature modules.
- Use interfaces for object shapes that might be extended, and type aliases for unions, intersections, and simple object types.
- Prefer readonly properties for immutable data.

```typescript
// types.ts
export interface Account {
  readonly id: string;
  name: string;
  currency: string;
  current_balance: number;
  color: string;
  group?: string;
}

export type AccountFormData = Omit<Account, 'id'>;
```

### Enums and Constants

- Use string literal unions instead of enums when possible.
- Define constants in dedicated `.ts` files (e.g., `constants.ts`) within feature modules.

```typescript
// constants.ts
export const ACCOUNT_TYPES = ['checking', 'savings', 'credit'] as const;
export type AccountType = typeof ACCOUNT_TYPES[number];
```

## React Patterns

### Functional Components

- Use functional components with hooks instead of class components.
- Use named exports for components that are not the default export of a file.
- Use default exports for the main component of a file.

```typescript
// Good
export default function AccountCard({ account }: Props) {
  // ...
}

// For multiple components in a file
export function AccountHeader({ title }: HeaderProps) {
  // ...
}
```

### Props

- Define prop types using TypeScript interfaces.
- Use destructuring for props in function parameters.
- Use optional chaining and nullish coalescing for handling optional props.

```typescript
interface ButtonProps extends React.ComponentProps<"button"> {
  variant?: "default" | "primary" | "secondary";
  size?: "sm" | "md" | "lg";
}

function Button({ variant = "default", size = "md", ...props }: ButtonProps) {
  // ...
}
```

### Hooks

- Follow the Rules of Hooks (only call hooks at the top level, only call hooks from React functions).
- Create custom hooks for reusable stateful logic.
- Use the `use` prefix for custom hooks.

```typescript
// hooks.ts
export function useAccounts() {
  const query = useQuery({
    queryKey: ['accounts'],
    queryFn: () => api.accounts.getAll(),
  });
  
  return query;
}
```

## Component Structure

### UI Component Hierarchy

1. **UI Primitives** (`components/ui/`): Basic UI components built on top of Radix UI.
2. **Input Components** (`components/inputs/`): Form input components.
3. **Elements** (`components/elements/`): Basic UI elements that are more specific than UI primitives.
4. **Blocks** (`components/blocks/`): Composite components that combine multiple elements.
5. **Feature Components** (`features/*/components/`): Domain-specific components.

### Component Composition

- Use the `asChild` pattern from Radix UI for component composition when appropriate.
- Use the `children` prop for component composition.
- Use the `as` prop pattern for polymorphic components when needed.

```typescript
function Button({
  asChild = false,
  ...props
}: ButtonProps & { asChild?: boolean }) {
  const Comp = asChild ? Slot : "button";
  return <Comp {...props} />;
}
```

### Component Variants

- Use `class-variance-authority` (CVA) for defining component variants.
- Define variants in a type-safe way with proper TypeScript types.

```typescript
const buttonVariants = cva(
  "base-styles-here",
  {
    variants: {
      variant: {
        default: "bg-primary text-white",
        secondary: "bg-secondary text-black",
      },
      size: {
        sm: "text-sm py-1 px-2",
        md: "text-base py-2 px-4",
      },
    },
    defaultVariants: {
      variant: "default",
      size: "md",
    },
  }
);

type ButtonProps = React.ComponentProps<"button"> & VariantProps<typeof buttonVariants>;
```

## State Management

### Local State

- Use `useState` for component-local state.
- Use `useReducer` for complex state logic within a component.

### Global State

- Use Zustand for global state management.
- Create separate stores for different domains.
- Use Immer for immutable state updates.

```typescript
// stores/accountStore.ts
import { create } from 'zustand';
import { immer } from 'zustand/middleware/immer';

interface AccountState {
  accounts: Account[];
  addAccount: (account: Account) => void;
}

export const useAccountStore = create<AccountState>()(
  immer((set) => ({
    accounts: [],
    addAccount: (account) => set((state) => {
      state.accounts.push(account);
    }),
  }))
);
```

### Server State

- Use TanStack Query (React Query) for server state management.
- Define query keys consistently.
- Use query options like `staleTime` and `cacheTime` appropriately.

```typescript
const accountsQuery = useQuery({
  queryKey: ['accounts'],
  queryFn: () => api.accounts.getAll(),
  staleTime: 5 * 60 * 1000, // 5 minutes
});
```

## Styling Approach

### Tailwind CSS

- Use Tailwind CSS for styling components.
- Use the `cn` utility function for merging class names.
- Follow the utility-first approach.

```typescript
import { cn } from "~/lib/utils";

function Button({ className, ...props }: ButtonProps) {
  return (
    <button
      className={cn(
        "px-4 py-2 bg-blue-500 text-white rounded",
        className
      )}
      {...props}
    />
  );
}
```

### Component Variants

- Use `class-variance-authority` for defining component variants.
- Define consistent variants across similar components.

### Dark Mode

- Use the `next-themes` package for dark mode support.
- Use Tailwind's dark mode classes for dark mode styles.

```tsx
<div className="bg-white text-black dark:bg-gray-900 dark:text-white">
  {/* Content */}
</div>
```

## Form Handling

### React Hook Form

- Use React Hook Form for form state management.
- Use Zod for form validation.
- Define validation schemas in dedicated files (e.g., `schemas.ts`).

```typescript
// schemas.ts
import { z } from 'zod';

export const accountSchema = z.object({
  name: z.string().min(1, "Name is required"),
  currency: z.string().min(1, "Currency is required"),
  current_balance: z.number(),
  color: z.string(),
  group: z.string().optional(),
});

// Component
import { useForm } from 'react-hook-form';
import { zodResolver } from '@hookform/resolvers/zod';

function AccountForm() {
  const form = useForm({
    resolver: zodResolver(accountSchema),
    defaultValues: {
      name: '',
      currency: 'USD',
      current_balance: 0,
      color: '#000000',
      group: '',
    },
  });
  
  // ...
}
```

## API Integration

### API Client

- Use Axios for API requests.
- Define API services in dedicated files.
- Use TanStack Query for data fetching and caching.

```typescript
// api/accounts.ts
import { axios } from '../api-config';
import type { Account } from '~/features/accounts/types';

export const accountsApi = {
  getAll: () => axios.get<Account[]>('/accounts'),
  getById: (id: string) => axios.get<Account>(`/accounts/${id}`),
  create: (data: Omit<Account, 'id'>) => axios.post<Account>('/accounts', data),
  update: (id: string, data: Partial<Account>) => axios.patch<Account>(`/accounts/${id}`, data),
  delete: (id: string) => axios.delete(`/accounts/${id}`),
};
```

## Common Pitfalls

### React Performance

- **Avoid Unnecessary Renders**: Use memoization techniques like `useMemo`, `useCallback`, and `React.memo` for expensive computations or to prevent unnecessary re-renders.
- **Optimize Lists**: Use stable keys for list items and consider virtualization for long lists.

### TypeScript Gotchas

- **Type Assertions**: Avoid using type assertions (`as`) when possible. Use type guards instead.
- **Non-null Assertions**: Be cautious with non-null assertions (`!`). They bypass TypeScript's null checks.

### State Management

- **Prop Drilling**: Avoid excessive prop drilling. Use context or state management libraries for deeply nested state.
- **Immutability**: Always treat state as immutable. Use Immer or spread operators for state updates.

### Accessibility

- **Keyboard Navigation**: Ensure all interactive elements are keyboard accessible.
- **ARIA Attributes**: Use appropriate ARIA attributes for custom components.
- **Color Contrast**: Ensure sufficient color contrast for text and UI elements.

### Error Handling

- **API Errors**: Properly handle and display API errors to users.
- **Form Validation**: Provide clear error messages for form validation errors.
- **Fallbacks**: Use error boundaries and fallback UI for handling unexpected errors.