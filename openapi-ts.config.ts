import { defineConfig } from "@hey-api/openapi-ts";

export default defineConfig({
  input: "http://127.0.0.1:5000/api/openapi.json",
  output: {
    format: "prettier",
    path: "src/api",
  },
  plugins: [
    "@hey-api/typescript",
    { name: "@hey-api/client-axios", baseUrl: "/api", runtimeConfigPath: "./src/api-config.ts" },
    { name: "@hey-api/sdk" },
    "@tanstack/react-query",
    "zod",
  ],
});
