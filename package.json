{"name": "webapp", "private": true, "version": "1.0.0", "description": "Finance.Pro Web Application", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "lint": "eslint .", "preview": "vite preview", "generate:client": "openapi-ts"}, "dependencies": {"@fontsource-variable/inter": "^5.2.5", "@hey-api/client-axios": "^0.6.3", "@hookform/resolvers": "^4.1.3", "@radix-ui/react-alert-dialog": "^1.1.6", "@radix-ui/react-avatar": "^1.1.3", "@radix-ui/react-dialog": "^1.1.6", "@radix-ui/react-dropdown-menu": "^2.1.6", "@radix-ui/react-label": "^2.1.2", "@radix-ui/react-popover": "^1.1.6", "@radix-ui/react-select": "^2.1.6", "@radix-ui/react-separator": "^1.1.7", "@radix-ui/react-slot": "^1.1.2", "@radix-ui/react-switch": "^1.1.3", "@radix-ui/react-toggle": "^1.1.2", "@radix-ui/react-toggle-group": "^1.1.2", "@radix-ui/react-tooltip": "^1.1.8", "@radix-ui/react-visually-hidden": "^1.2.2", "@tanstack/react-query": "^5.69.3", "@tanstack/react-router": "^1.114.29", "apexcharts": "^4.5.0", "axios": "^1.8.4", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "date-fns": "^4.1.0", "immer": "^10.1.1", "lucide-react": "^0.485.0", "next-themes": "^0.4.6", "react": "^19.0.0", "react-apexcharts": "^1.7.0", "react-dom": "^19.0.0", "react-hook-form": "^7.54.2", "react-use": "^17.6.0", "sonner": "^2.0.2", "tailwind-merge": "^3.0.2", "tailwindcss-animate": "^1.0.7", "zod": "^3.24.2", "zustand": "^5.0.3"}, "devDependencies": {"@eslint/js": "^9.23.0", "@hey-api/openapi-ts": "^0.64.15", "@ianvs/prettier-plugin-sort-imports": "^4.4.1", "@tailwindcss/vite": "^4.0.17", "@tanstack/eslint-plugin-query": "^5.68.0", "@tanstack/react-query-devtools": "^5.69.3", "@tanstack/react-router-devtools": "^1.114.29", "@tanstack/router-plugin": "^1.114.29", "@types/node": "^22.13.14", "@types/react": "^19.0.12", "@types/react-dom": "^19.0.4", "@vitejs/plugin-react-swc": "^3.8.1", "eslint": "^9.23.0", "eslint-plugin-react": "^7.37.4", "eslint-plugin-react-hooks": "^5.2.0", "eslint-plugin-react-refresh": "^0.4.19", "globals": "^16.0.0", "prettier": "^3.5.3", "prettier-plugin-tailwindcss": "^0.6.11", "tailwindcss": "^4.0.17", "typescript": "~5.8.2", "typescript-eslint": "^8.28.0", "vite": "^6.2.3", "vite-tsconfig-paths": "^5.1.4"}, "packageManager": "pnpm@10.8.0+sha512.0e82714d1b5b43c74610193cb20734897c1d00de89d0e18420aebc5977fa13d780a9cb05734624e81ebd81cc876cd464794850641c48b9544326b5622ca29971", "pnpm": {"onlyBuiltDependencies": ["@swc/core", "esbuild"]}}