server {
    listen 80;
    server_name _;
    root /usr/share/nginx/html;
    index index.html;

    # Security headers
    add_header X-Frame-Options "SAMEORIGIN";
    add_header X-Content-Type-Options "nosniff";
    add_header X-XSS-Protection "1; mode=block";
    add_header Referrer-Policy "strict-origin-when-cross-origin";
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self' https:;";

    # Cache static assets
    location ~* \.(jpg|jpeg|png|gif|ico|css|js|svg|woff|woff2|ttf|eot)$ {
        expires 30d;
        add_header Cache-Control "public, max-age=2592000";
        try_files $uri =404;
    }

    # Handle site.webmanifest
    location = /site.webmanifest {
        add_header Content-Type application/manifest+json;
        expires 30d;
        add_header Cache-Control "public, max-age=2592000";
    }

    # Handle API requests - adjust this based on your API endpoints
    location /api/ {
        # If you have an API server, proxy requests to it
        # proxy_pass http://api-server:port/;
        # For now, we'll return 404 for API requests
        return 404;
    }

    # Handle SPA routing - redirect all non-file requests to index.html
    location / {
        try_files $uri $uri/ /index.html;
        add_header Cache-Control "no-cache, no-store, must-revalidate";
    }

    # Handle 404 errors
    error_page 404 /index.html;
}
